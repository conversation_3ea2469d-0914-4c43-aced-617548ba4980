import React from 'react';
import { Box, Stack, TextField } from '@mui/material';
import { placeholders, strings } from '@/utility/strings';
import { QuestionText } from '../components/QuestionText';

export const Paragraph = (props) => {
  const { question, DescriptionComponent } = props;

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <TextField multiline minRows={3} placeholder={placeholders.yourResponse} />
      </Stack>
    </Box>
  );
};
