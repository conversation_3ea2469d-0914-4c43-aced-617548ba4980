import React, { useContext } from 'react';
import { QuestionnaireDetail } from '../Questionnaire/components/QuestionnaireDetail';
import { QuestionnaireDetailsContext } from '@/context';
import { Paper, Stack, Typography } from '@mui/material';
import { strings } from '@/utility/strings';

export const Properties = (props) => {
  const { getQuestionnaireDetails } = props;

  return (
    <>
      <Paper sx={{ border: 'none' }}>
        <Stack direction="row" alignItems="center">
          <Typography variant="h3">{strings.properties}</Typography>
        </Stack>
      </Paper>
      <div style={{ marginTop: '1rem' }}>
        <QuestionnaireDetail getQuestionnaireDetails={getQuestionnaireDetails} />
      </div>
    </>
  );
};
