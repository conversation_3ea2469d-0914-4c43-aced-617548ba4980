import React from 'react';
import { Box, Stack, TextField } from '@mui/material';
import { placeholders } from '@/utility/strings';
import { QuestionText } from '../components/QuestionText';

export const Number = (props) => {
  const { question, DescriptionComponent } = props;

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <TextField placeholder={placeholders.yourResponse} />
      </Stack>
    </Box>
  );
};
