import React, { useMemo } from 'react';
import { Typography } from '@mui/material';
import { extractExtension } from '@/utility/utils';
import { CANNOT_LOCATE_STRING } from '@/containers/CommonConstants';

export const Description = (props) => {
  const { question } = props || {};

  const descriptionExtension = useMemo(() => {
    return question?.extension?.find((extension) => extension.url === 'Item/description') || {};
  }, [question.extension]);

  let descriptionText =
    descriptionExtension?.valueString !== CANNOT_LOCATE_STRING ? descriptionExtension?.valueString : '';

  return (
    <>
      {descriptionText && (
        <Typography variant="body2" sx={{ my: 2 }} dangerouslySetInnerHTML={{ __html: descriptionText }} />
      )}
    </>
  );
};
