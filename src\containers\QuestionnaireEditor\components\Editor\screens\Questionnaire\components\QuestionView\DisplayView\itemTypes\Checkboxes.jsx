import React, { useMemo } from 'react';
import { placeholders, strings } from '@/utility/strings';
import { Box, Checkbox, FormControlLabel, FormGroup, Stack, TextField, styled } from '@mui/material';
import { extractExtension, extractOtherOptionDetails } from '@/utility/utils';
import { QuestionText } from '../components/QuestionText';

const StyledFormControlLabel = styled((props) => <FormControlLabel {...props} />)(({ theme, selected }) => ({
  '.MuiFormControlLabel-label': selected && {
    color: theme.palette.primary.main,
  },
}));

export const Checkboxes = (props) => {
  const { question, DescriptionComponent } = props;
  const isHorizontal = useMemo(() => {
    const horizontalOrientationExtension = question.extension.find(
      (extension) => extension.url === 'Item/horizontal-orientation',
    );
    return horizontalOrientationExtension ? horizontalOrientationExtension.valueBoolean : false;
  }, [question.extension]);

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}

        <FormGroup name="checkbox-group" sx={{ pointerEvents: 'none' }}>
          <Stack gap={1} direction={isHorizontal ? 'row' : 'column'} flexWrap="wrap">
            {question?.answerOption?.map((item, index) => {
              const otherOptionExtension = extractExtension(
                item.valueCoding.extension,
                'Item/AnswerOption/ValueCoding/other-option',
              );
              const otherOptionAvailable = otherOptionExtension ? otherOptionExtension.valueString : '';
              const { otherOptionId, otherOptionValue } = extractOtherOptionDetails(item.valueCoding.extension);

              return (
                <Stack
                  key={index}
                  direction={{ xs: 'column', sm: 'row' }}
                  alignItems={{ xs: 'flex-start', sm: 'center' }}
                >
                  <StyledFormControlLabel sx={{ mb: -0.5 }} label={item.valueCoding.display} control={<Checkbox />} />
                  {otherOptionAvailable && otherOptionId && item.valueCoding.id === Number(otherOptionId) && (
                    <TextField type="text" autoComplete="off" placeholder={placeholders.otherResponse} />
                  )}
                </Stack>
              );
            })}
          </Stack>
        </FormGroup>
      </Stack>
    </Box>
  );
};
