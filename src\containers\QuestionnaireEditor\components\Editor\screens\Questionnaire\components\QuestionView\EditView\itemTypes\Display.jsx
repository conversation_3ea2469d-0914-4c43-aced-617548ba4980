import { ValidationErrorContext } from '@/context';
import { placeholders, strings } from '@/utility/strings';
import { FormControl, TextField, Typography, FormHelperText, FormLabel } from '@mui/material';
import { characterLength } from '@/containers/CommonConstants';
import React, { useContext, useState } from 'react';

export const Display = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;
  const [questionText, setQuestionText] = useState(question?.text || '');

  const handleCreateInformationQuestion = (event) => {
    // setQuestionText(event.target.value);
    const newText = event.target.value.slice(0, characterLength.informationTextLength);
    setQuestionText(newText);
    let newQuestion = {
      ...question,
      type: 'display',
      text: event.target.value,
    };
    handleCreateQuestion(pageIndex, questionIndex, newQuestion);
  };

  const { questionErrors } = useContext(ValidationErrorContext);

  const error = questionErrors[question.linkId]?.text;

  return (
    <>
      <Typography sx={{ mb: 1 }}>
        <FormLabel required={true} sx={{ color: '#000' }}>
          {strings.item}
        </FormLabel>
      </Typography>
      <FormControl fullWidth>
        <TextField
          value={questionText}
          onChange={handleCreateInformationQuestion}
          placeholder={placeholders.item}
          error={!!error}
          helperText={error}
          multiline
          inputProps={{ maxLength: characterLength.informationTextLength }}
        />
        {questionText.length >= characterLength.informationTextLength && (
          <FormHelperText
            sx={{ textAlign: 'right', color: '#bdc1cc' }}
          >{`${characterLength.informationTextLength} / ${characterLength.informationTextLength}`}</FormHelperText>
        )}
      </FormControl>
      {DescriptionComponent}
    </>
  );
};
