import React, { createContext, useEffect, useState } from 'react';
import { extractExtension } from '@/utility/utils';

// Create the context
export const QuestionnaireReportContext = createContext();

// Create a provider component
export const QuestionnaireReportProvider = ({
  children,
  existingQuestionnaireData,
  existingPdfTemplate,
  codebookDefaultTemplate,
  htmlReportDefaultTemplate,
}) => {
  // Define the initial state of your context
  console.log(existingQuestionnaireData);
  const [initialState, setInitialState] = useState({
    reportHtml:
      extractExtension(existingQuestionnaireData?.extension, 'htmltemplate-base64')?.valueString ||
      htmlReportDefaultTemplate,
    reportPdfTemplate: {
      fileName: extractExtension(existingQuestionnaireData?.extension, 'pdftemplate-name')?.valueString || '',
      fileUrl: '',
      fileBase64: existingPdfTemplate || '',
      type: '',
      size: '',
    },
    codeBookHtml:
      extractExtension(existingQuestionnaireData?.extension, 'codeBookHtmlData')?.valueString ||
      codebookDefaultTemplate,
  });

  const [questionnaireReport, setQuestionnaireReport] = useState(initialState);

  useEffect(() => {
    if (existingQuestionnaireData) {
      setInitialState({
        reportHtml:
          extractExtension(existingQuestionnaireData?.extension, 'htmltemplate-base64')?.valueString ||
          htmlReportDefaultTemplate,
        reportPdfTemplate: {
          fileName: extractExtension(existingQuestionnaireData?.extension, 'pdftemplate-name')?.valueString || '',
          fileUrl: '',
          fileBase64: extractExtension(existingQuestionnaireData?.extension, 'pdftemplate-base64')?.valueString || '',
          type: '',
          size: '',
        },
        codeBookHtml:
          extractExtension(existingQuestionnaireData?.extension, 'codeBookHtmlData')?.valueString ||
          codebookDefaultTemplate,
      });
    }
  }, [existingQuestionnaireData?.extension]);

  const handleQuestionnaireReportContext = (updatedDetail) => {
    setQuestionnaireReport((prevValue) => ({ ...prevValue, ...updatedDetail }));
  };

  return (
    <QuestionnaireReportContext.Provider
      value={{ questionnaireReport, handleQuestionnaireReportContext, initialState }}
    >
      {children}
    </QuestionnaireReportContext.Provider>
  );
};
