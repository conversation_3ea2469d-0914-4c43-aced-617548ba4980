import React, { useContext, useMemo, useState } from 'react';
import { FormControl, Stack, TextField, FormHelperText } from '@mui/material';
import { QuestionTextInput } from './components/QuestionTextInput';
import { placeholders, strings } from '@/utility/strings';
import { extractExtension, mergeTwoExtension } from '@/utility/utils';
import { ValidationErrorContext } from '@/context';
import { characterLength } from '@/containers/CommonConstants';

export const NumericSlider = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;
  const { questionErrors } = useContext(ValidationErrorContext);
  const error = useMemo(() => questionErrors[question.linkId], [questionErrors]);

  const EXTENSION_BASE_URL = 'Item/slider-';
  const MIN_VALUE = 0;
  const MAX_VALUE = 100;
  const DEFAULT_STEP = 10;

  const possibleExtensionUrls = [
    `${EXTENSION_BASE_URL}min-value`,
    `${EXTENSION_BASE_URL}min-label`,
    `${EXTENSION_BASE_URL}max-value`,
    `${EXTENSION_BASE_URL}max-label`,
    `${EXTENSION_BASE_URL}steps`,
  ];

  const { minValue, maxValue, minLabel, maxLabel, steps } = useMemo(() => {
    if (question?.extension?.length) {
      const minLabel = extractExtension(question?.extension, EXTENSION_BASE_URL + 'min-label')?.valueString || '';
      const maxLabel = extractExtension(question?.extension, EXTENSION_BASE_URL + 'max-label')?.valueString || '';
      const minValue =
        extractExtension(question?.extension, EXTENSION_BASE_URL + 'min-value')?.valueDecimal || MIN_VALUE;
      const maxValue =
        extractExtension(question?.extension, EXTENSION_BASE_URL + 'max-value')?.valueDecimal || MAX_VALUE;
      const steps = extractExtension(question?.extension, EXTENSION_BASE_URL + 'steps')?.valueDecimal || DEFAULT_STEP;

      return { minValue, maxValue, minLabel, maxLabel, steps };
    }
    return {
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      minLabel: '',
      maxLabel: '',
      steps: DEFAULT_STEP,
    };
  }, [question.extension]);

  const [questionText, setQuestionText] = useState(question?.text || '');
  const [sliderConfigurationExtension, setSliderConfigurationExtension] = useState(
    question?.extension.filter((extension) => possibleExtensionUrls.includes(extension.url)) || [],
  );
  const [sliderConfig, setSliderConfig] = useState({
    'min-value': minValue,
    'max-value': maxValue,
    'min-label': minLabel,
    steps: steps,
    'max-label': maxLabel,
  });

  const handleQuestionCreation = (question) => {
    handleCreateQuestion(pageIndex, questionIndex, { ...question, type: 'integer' });
  };

  const handleQuestionTextChange = (event) => {
    // setQuestionText(event.target.value);
    const newText = event.target.value.slice(0, characterLength.itemTextLength);
    setQuestionText(newText);
    let newQuestion = {
      ...question,
      text: newText,
    };
    handleQuestionCreation(newQuestion);
  };

  const handleNumericSliderConfigurations = (event) => {
    const { name, value } = event.target || {};
    const newExtension = sliderConfigurationExtension.map((item) => {
      if (item.url === `${EXTENSION_BASE_URL}${name}`) {
        return {
          ...item,
          [name.includes('label') ? 'valueString' : 'valueDecimal']: name.includes('label') ? value : Number(value),
        };
      }
      return item;
    });

    let newSliderConfig = { ...sliderConfig };
    newSliderConfig[name] = value;

    setSliderConfig(newSliderConfig);
    setSliderConfigurationExtension(newExtension);
    handleQuestionCreation({
      ...question,
      sliderConfig: newSliderConfig,
      extension: mergeTwoExtension(question.extension, newExtension),
    });
  };

  return (
    <>
      <QuestionTextInput
        value={questionText}
        onChange={handleQuestionTextChange}
        question={question}
        characterLimit={characterLength.itemTextLength}
      />
      {DescriptionComponent}
      <Stack spacing={2} sx={{ mt: 3 }}>
        <Stack direction="row" spacing={2}>
          <FormControl>
            <TextField
              type="number"
              name="min-value"
              value={sliderConfig['min-value']}
              label={strings.minValue}
              onChange={handleNumericSliderConfigurations}
              error={error && !!error['sliderConfig.min-value']}
              helperText={error && error['sliderConfig.min-value']}
              InputLabelProps={{ style: { color: 'black' } }}
            />
          </FormControl>
          <FormControl>
            <TextField
              name="min-label"
              value={sliderConfig['min-label']}
              placeholder={placeholders.minValueLabel}
              onChange={handleNumericSliderConfigurations}
              error={error && !!error['sliderConfig.min-label']}
              helperText={error && error['sliderConfig.min-label']}
              inputProps={{ maxLength: characterLength.labelLength }}
            />
            {sliderConfig['min-label'].length >= characterLength.labelLength && (
              <FormHelperText
                sx={{ textAlign: 'right', color: '#bdc1cc' }}
              >{`${characterLength.labelLength} / ${characterLength.labelLength}`}</FormHelperText>
            )}
          </FormControl>
        </Stack>
        <Stack direction="row" spacing={2}>
          <FormControl>
            <TextField
              type="number"
              name="max-value"
              value={sliderConfig['max-value']}
              label={strings.maxValue}
              onChange={handleNumericSliderConfigurations}
              error={error && !!error['sliderConfig.max-value']}
              helperText={error && error['sliderConfig.max-value']}
              InputLabelProps={{ style: { color: 'black' } }}
            />
          </FormControl>
          <FormControl>
            <TextField
              name="max-label"
              value={sliderConfig['max-label']}
              placeholder={placeholders.maxValueLabel}
              onChange={handleNumericSliderConfigurations}
              error={error && !!error['sliderConfig.max-label']}
              helperText={error && error['sliderConfig.max-label']}
              inputProps={{ maxLength: characterLength.labelLength }}
            />
            {sliderConfig['max-label'].length >= characterLength.labelLength && (
              <FormHelperText
                sx={{ textAlign: 'right', color: '#bdc1cc' }}
              >{`${characterLength.labelLength} / ${characterLength.labelLength}`}</FormHelperText>
            )}
          </FormControl>
        </Stack>
        <Stack direction="row" spacing={2}>
          <FormControl>
            <TextField
              type="number"
              name="steps"
              value={sliderConfig['steps']}
              label="Increment"
              onChange={handleNumericSliderConfigurations}
              error={error && !!error['sliderConfig.steps']}
              helperText={error && error['sliderConfig.steps']}
              InputLabelProps={{ style: { color: 'black' } }}
            />
          </FormControl>
        </Stack>
      </Stack>
    </>
  );
};
