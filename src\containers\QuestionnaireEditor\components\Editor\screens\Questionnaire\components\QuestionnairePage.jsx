import React, { useContext, useEffect, useState } from 'react';
import { Typography, Paper, Stack, Box, Button, IconButton } from '@mui/material';
import { MenuList } from '@/components';
import { CambianTooltip } from '@/components';
import { FileCopy, Delete, MoreVert, Add } from '@mui/icons-material';
import { strings } from '@/utility/strings';
import { Questions } from './Questions';
import { useGenerateLinkId } from '@/hooks/useGenerateLinkId';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { QuestionnaireContext } from '@/context';
import { ADD_ITEM, ADD_PAGE, DELETE_PAGE, DUPLICATE_PAGE, UPDATE_PAGES } from '@/context/questionnairePages';

export const QuestionnairePage = (props) => {
  const { generateItemLinkId, generatePageLinkId, updateLastQuestionLinkId } = useGenerateLinkId();
  const { questionnaireState: questionnairePages, dispatchQuestionnaireAction } = useContext(QuestionnaireContext);
  console.log('questions context', questionnairePages);
  const [anchorEl, setAnchorEl] = useState(null);
  const [currentMenuPageIndex, setCurrentMenuPageIndex] = useState(null);

  const handleMenuIconClick = (event, index) => {
    setCurrentMenuPageIndex(index);
    setAnchorEl(event.currentTarget);
  };

  const handleAddPageClick = () => {
    const nextPageLinkId = generatePageLinkId(questionnairePages);
    const nextItemLinkId = generateItemLinkId(questionnairePages);
    dispatchQuestionnaireAction({ type: ADD_PAGE, nextPageLinkId, nextItemLinkId });
  };

  const handlePageDuplicate = (pageIndex) => {
    const nextPageLinkId = generatePageLinkId(questionnairePages);
    const nextItemLinkId = generateItemLinkId(questionnairePages);
    dispatchQuestionnaireAction({
      type: DUPLICATE_PAGE,
      pageIndex,
      nextPageLinkId,
      nextItemLinkId,
      updateLastQuestionLinkId,
    });
  };

  const handleDeletePage = (pageIndex) => {
    dispatchQuestionnaireAction({ type: DELETE_PAGE, pageIndex });
  };

  const handleAddQuestion = (pageIndex) => {
    const nextItemLinkId = generateItemLinkId(questionnairePages);
    dispatchQuestionnaireAction({ type: ADD_ITEM, pageIndex, nextItemLinkId });
  };

  const pageMenuItems = [
    {
      id: 0,
      label: strings.delete,
      icon: <Delete sx={{ mr: 1 }} fontSize="small" />,
      handleClick: () => handleDeletePage(currentMenuPageIndex),
      show: true,
    },
    {
      id: 1,
      label: strings.duplicate,
      icon: <FileCopy sx={{ mr: 1 }} fontSize="small" />,
      handleClick: () => handlePageDuplicate(currentMenuPageIndex),
      show: true,
    },
  ];

  const moveCard = (fromPageIndex, fromQuestionIndex, toPageIndex, toQuestionIndex) => {
    const newPages = structuredClone(questionnairePages);
    const [removed] = newPages[fromPageIndex].item.splice(fromQuestionIndex, 1);
    newPages[toPageIndex].item.splice(toQuestionIndex, 0, removed);
    console.log('new pages', newPages);

    dispatchQuestionnaireAction({ type: UPDATE_PAGES, updatedPages: newPages });
  };

  return (
    <>
      <Paper sx={{ border: 'none' }}>
        <Stack direction="flex-start" alignItems="center">
          <Typography variant="h3">{strings.items}</Typography>
        </Stack>
      </Paper>
      <DndProvider backend={HTML5Backend}>
        {questionnairePages.map((page, pageIndex) => (
          <Box sx={{ mt: 2 }} key={pageIndex}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography>
                {strings.page} {pageIndex + 1}
              </Typography>
              <MoreVert
                onClick={(event) => handleMenuIconClick(event, pageIndex)}
                sx={{ cursor: 'pointer' }}
              />
              <MenuList menuItems={pageMenuItems} anchorEl={anchorEl} setAnchorEl={setAnchorEl} />
            </Stack>
            <DndProvider backend={HTML5Backend}>
              {page.item.map((question, questionIndex) => (
                <Questions
                  key={questionIndex}
                  pageIndex={pageIndex}
                  questionIndex={questionIndex}
                  moveCard={moveCard}
                  question={question}
                />
              ))}
            </DndProvider>

            <Button
              variant="text"
              startIcon={<Add />}
              onClick={() => handleAddQuestion(pageIndex)}
              sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' }, padding: '6px 1px' }}
            >
              {strings.addItem}
            </Button>
          </Box>
        ))}
      </DndProvider>

      <Paper sx={{ mt: 5, p: 2, display: 'flex', justifyContent: 'center' }}>
        <Button
          variant="text"
          startIcon={<Add />}
          onClick={handleAddPageClick}
          sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
        >
          {strings.addPage}
        </Button>
      </Paper>
    </>
  );
};
