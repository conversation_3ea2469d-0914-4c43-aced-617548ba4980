import { CANNOT_LOCATE_STRING, selectionRules, selectionNames } from '@/containers/CommonConstants';
import { generate } from 'short-uuid';

export const generateShortUuid = () => {
  return generate();
};

export const removeHtmlTags = (str) => {
  const htmlTagsRemovedString = new DOMParser().parseFromString(str, 'text/html');
  return htmlTagsRemovedString.body.textContent || '';
};

export const extractExtension = (extensionArray, url) => {
  let matchingExtension = null;
  if (extensionArray !== undefined && Array.isArray(extensionArray) && url !== undefined && url !== null) {
    matchingExtension =
      extensionArray.find((extension) => extension?.url?.toUpperCase() === url?.toUpperCase()) || null;
  }
  return matchingExtension;
};

export const downloadFileInJsonFormat = (jsonString, fileName) => {
  const blob = new Blob([jsonString], { type: 'application/json' });
  const href = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = href;
  link.download = fileName + '.json';
  document.body.appendChild(link);
  link.click();

  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};

export const decorateWithQuestionAndPageSequence = (questionnaireState) => {
  let newQuestionnaireState = structuredClone(questionnaireState);
  newQuestionnaireState?.forEach((page, pageIndex) => {
    const questions = page.item;
    let extensions = page.extension || [];

    // Check if the page-level extension is already present
    const pageExtensionUrl = 'Item/question-group-sequence';
    if (!extensions.some((ext) => ext.url === pageExtensionUrl)) {
      extensions.push({ url: pageExtensionUrl, valueInteger: pageIndex + 1 });
    }
    page.extension = extensions;

    questions?.forEach((question, questionIndex) => {
      let questionExtensions = question.extension || [];

      const questionSequenceUrl = 'Item/question-in-group-sequence';
      const questionGroupSequenceUrl = 'Item/question-group-sequence';

      // Check if the question-level extensions are already present
      if (!questionExtensions.some((ext) => ext.url === questionSequenceUrl)) {
        questionExtensions.push({ url: questionSequenceUrl, valueInteger: questionIndex + 1 });
      }
      if (!questionExtensions.some((ext) => ext.url === questionGroupSequenceUrl)) {
        questionExtensions.push({ url: questionGroupSequenceUrl, valueInteger: pageIndex + 1 });
      }

      question.extension = questionExtensions;
    });
  });
  return newQuestionnaireState;
};

export const extractOtherOptionDetails = (extension) => {
  const otherOptionExtension = extractExtension(extension, 'Item/AnswerOption/ValueCoding/other-option');
  const otherOptionAvailable = otherOptionExtension ? otherOptionExtension.valueString : '';
  let otherOptionId;
  let otherOptionValue = '';
  if (otherOptionAvailable) {
    let otherOptionParts = otherOptionAvailable.split(',');
    otherOptionId = otherOptionParts[0].split(':')[1];
    otherOptionValue = otherOptionParts[1] ? otherOptionParts[1].split(':')[1] : '';
  }

  return { otherOptionId, otherOptionValue };
};

export function isRadioButtonQuestion(question) {
  let isRadioQuestion = false;
  if (question.type === 'choice') {
    let multipleChoiceExtension = extractExtension(question.extension, 'Item/multiple-answer-choice');
    if (multipleChoiceExtension !== null) {
      isRadioQuestion = !multipleChoiceExtension.valueBoolean;
    }
  }
  return isRadioQuestion;
}

export function isCheckboxQuestion(question) {
  let isCheckboxQuestion = false;
  if (question.type === 'choice') {
    let multipleChoiceExtension = extractExtension(question.extension, 'Item/multiple-answer-choice');
    if (multipleChoiceExtension !== null) {
      isCheckboxQuestion = multipleChoiceExtension.valueBoolean;
    }
  }
  return isCheckboxQuestion;
}

export function isDropdownQuestion(question) {
  let dropdownQuestion = false;
  if (question.type === 'choice') {
    let dropdownExtension = extractExtension(question.extension, 'Item/question-type-id');

    if (dropdownExtension && dropdownExtension.valueInteger === 5543) {
      dropdownQuestion = true;
    }
  }
  return dropdownQuestion;
}

export function isLargeButtonQuestion(question) {
  let largeButtonQuestion = false;

  if (question.type === 'choice') {
    let largeButtonExtension = extractExtension(question.extension, 'Item/display-large-buttons');
    if (largeButtonExtension && largeButtonExtension.valueBoolean) {
      largeButtonQuestion = true;
    }
  }

  return largeButtonQuestion;
}

export function isNumericSliderQuestion(question) {
  let numericSliderQuestion = false;
  if (question.type === 'integer') {
    let multipleChoiceExtension = extractExtension(question.extension, 'Item/display-type');
    if (multipleChoiceExtension !== null) {
      numericSliderQuestion = 'numeric-slider' === multipleChoiceExtension.valueString;
    }
  }
  return numericSliderQuestion;
}

export function isLinearScaleQuestion(question) {
  let linearScaleQuestion = false;

  if (question.type === 'choice') {
    let multipleChoiceExtension = extractExtension(question.extension, 'Item/display-type');
    if (multipleChoiceExtension !== null) {
      linearScaleQuestion = multipleChoiceExtension.valueString === 'choice-bar';
    }
  }
  return linearScaleQuestion;
}

export function isBodyDiagramQuestion(question) {
  let bodyDiagramQuestion = false;
  if (question.type === 'choice') {
    let multipleChoiceExtension = extractExtension(question.extension, 'Item/display-type');
    if (multipleChoiceExtension !== null) {
      bodyDiagramQuestion = 'choice-image' === multipleChoiceExtension.valueString;
    }
  }
  return bodyDiagramQuestion;
}

export const isIntegerOnlyQuestion = (question) => {
  let integerQuestion = false;
  if (question.type === 'integer') {
    let integerOnlyExtension = extractExtension(question.extension, 'Item/integer-only');
    if (integerOnlyExtension !== null) {
      integerQuestion = integerOnlyExtension.valueBoolean;
    }
  }
  return integerQuestion;
};

export const mergeTwoExtension = (extension1, extension2) => {
  // param1, param2 should be extension array, it will merge param2, into param1, duplicates will be overridden
  // from param2
  let extension2Map = new Map(extension2.map((item) => [item.url, item]));

  const mergedExtension = extension1
    .map((item) => (extension2Map.has(item.url) ? extension2Map.get(item.url) : item))
    .concat(extension2.filter((item) => !extension1.some((extension) => extension.url === item.url)));

  return mergedExtension;
};

export const cleanQuestionnaireItem = (questionnaireData) => {
  // * used for removing keys like explanation, description, sliderConfig, validationRule, linearConfig
  // * these are not required in fhir structure

  return questionnaireData.map((group) => ({
    ...group,
    item: group.item.map((item) => {
      const cleanedItem = {
        ...item,
      };
      // Delete specified properties directly
      delete cleanedItem.isEditMode;
      delete cleanedItem.description;
      delete cleanedItem.explanation;
      delete cleanedItem.validationRule;
      delete cleanedItem.sliderConfig;
      delete cleanedItem.linearConfig;
      return cleanedItem;
    }),
  }));
};

const getExistingValidationRules = (conditions) => {
  const EXTENSION_BASE_URL = 'Item';
  let minCondition = conditions.find((rule) => rule.url === `${EXTENSION_BASE_URL}/min-value`) || {};
  let maxCondition = conditions.find((rule) => rule.url === `${EXTENSION_BASE_URL}/max-value`) || {};
  let minExclusion = conditions.find((rule) => rule.url === `${EXTENSION_BASE_URL}/min-exclusion`) || {};
  let maxExclusion = conditions.find((rule) => rule.url === `${EXTENSION_BASE_URL}/max-exclusion`) || {};

  let operator;
  let from;
  let to;

  if (minExclusion && minExclusion.valueBoolean && minCondition?.valueDecimal) {
    operator = '>';
    from = minCondition.valueDecimal;
  } else if (maxExclusion && maxExclusion.valueBoolean && maxCondition?.valueDecimal) {
    operator = '<';
    to = maxCondition.valueDecimal;
  } else if (!minExclusion.valueBoolean && !maxExclusion.valueBoolean) {
    if (minCondition?.valueDecimal && maxCondition?.valueDecimal) {
      operator = '<<';
      from = minCondition.valueDecimal;
      to = maxCondition.valueDecimal;
    } else if (minCondition?.valueDecimal) {
      operator = '>=';
      from = minCondition.valueDecimal;
    } else if (maxCondition?.valueDecimal) {
      operator = '<=';
      to = maxCondition.valueDecimal;
    }
  }

  return {
    operator,
    from,
    to,
  };
};

const getValidationRule = (extensions) => {
  const rule = getExistingValidationRules(extensions);
  return rule ? { ...rule, 'min-value': rule.from || '', 'max-value': rule.to || '' } : null;
};

const getSliderConfig = (extensions) => {
  const minValueExt = extensions.find((ext) => ext.url === 'Item/slider-min-value');
  const maxValueExt = extensions.find((ext) => ext.url === 'Item/slider-max-value');
  const minLabelExt = extensions.find((ext) => ext.url === 'Item/slider-min-label');
  const maxLabelExt = extensions.find((ext) => ext.url === 'Item/slider-max-label');
  const sliderStepsExt = extensions.find((ext) => ext.url === 'Item/slider-steps');

  return {
    'min-value': minValueExt?.valueDecimal || 0,
    'max-value': maxValueExt?.valueDecimal || 100,
    'min-label': minLabelExt?.valueString || 'min',
    'max-label': maxLabelExt?.valueString || 'max',
    steps: sliderStepsExt?.valueDecimal || 10,
  };
};

const getLinearConfig = (item) => {
  const startLabelExt = item.extension.find((ext) => ext.url === 'Item/bar-start-label');
  const endLabelExt = item.extension.find((ext) => ext.url === 'Item/bar-end-label');
  return {
    'start-value': item.answerOption[0]?.valueCoding?.display,
    'end-value': item.answerOption[item.answerOption.length - 1]?.valueCoding?.display,
    'start-label': startLabelExt?.valueString || '',
    'end-label': endLabelExt?.valueString || '',
  };
};

export const addKeysToQuestionnaireItem = (data) => {
  if (!data) return;
  const newData = structuredClone(data);

  newData.forEach((group) => {
    group.item.forEach((item) => {
      // set editMode to false
      item.isEditMode = false;
      // Add description if available
      const descriptionExtension = item.extension.find((ext) => ext.url === 'Item/description');
      if (descriptionExtension?.valueString && descriptionExtension?.valueString !== CANNOT_LOCATE_STRING) {
        item.description = descriptionExtension.valueString;
      }

      // Add explanation with type if available
      const explanationExtension = item.extension.find((ext) => ext.url === 'Item/explanation');
      if (explanationExtension?.valueString && explanationExtension?.valueString !== CANNOT_LOCATE_STRING) {
        item.explanation = {
          text: explanationExtension.valueString,
          type: item.extension.find((ext) => ext.url === 'Item/explanation-flag')?.valueString || '',
        };
      }

      // Add validation rule for integer and decimal questions
      if (item.type === 'integer' || item.type === 'decimal') {
        item.validationRule = getValidationRule(item.extension);
      }

      // Add sliderConfig for integer questions with display-type = 'numeric-slider'
      if (
        item.type === 'integer' &&
        item.extension.find((ext) => ext.url === 'Item/display-type')?.valueString === 'numeric-slider'
      ) {
        item.sliderConfig = getSliderConfig(item.extension);
      }

      // Add linearConfig for choice questions with display-type = 'choice-bar'
      if (
        item.type === 'choice' &&
        item.extension.find((ext) => ext.url === 'Item/display-type')?.valueString === 'choice-bar'
      ) {
        item.linearConfig = getLinearConfig(item);
      }
    });
  });

  return newData;
};

export const addKeysToScoringItems = (data) => {
  const parsedState = [];

  const scoreDefinitionsArray = data?.filter((extension) => extension.url === 'list-of-score-definitions');

  if (scoreDefinitionsArray) {
    scoreDefinitionsArray.forEach((scoreDefinitions) => {
      const scoreId = scoreDefinitions.extension.find((extension) => extension.url === 'score-id')?.valueCode;
      const scoreName = scoreDefinitions.extension.find((extension) => extension.url === 'score-name')?.valueString;
      const formulaDefinitions = scoreDefinitions.extension.find(
        (extension) => extension.url === 'list-of-formula-definitions',
      );
      let allFormulae = [];

      if (formulaDefinitions && formulaDefinitions.extension && Array.isArray(formulaDefinitions.extension)) {
        formulaDefinitions.extension
          .filter((ext) => ext.url === 'set-of-api-formula')
          .map((formula) => {
            const formulaName = formula.extension.find((extension) => extension.url === 'formula-name')?.valueString;

            const mathematicalExpression = formula.extension.find(
              (extension) => extension.url === 'mathematical-expression',
            )?.valueString;

            const selectionRule = formula.extension.find(
              (extension) => extension.url === 'selection-rule',
            )?.valueString;

            let selectionName = selectionNames.selectRule;
            let arithmetic = '';
            let subSetCount = 0;

            if (selectionRule && selectionRule != selectionNames.selectRule) {
              var selectionRuleTypeArr = selectionRule.split('$');

              if (selectionRuleTypeArr[0] === selectionRules.numQEq) {
                selectionName = selectionNames.requiredResponsesCountFromSubset;
                if (selectionRuleTypeArr.length >= 2) {
                  subSetCount = parseInt(selectionRuleTypeArr[1], 10) || 0;
                }
              } else if (selectionRuleTypeArr[0] === selectionRules.varComapre) {
                selectionName = selectionNames.arithmeticComparison;
                arithmetic = selectionRuleTypeArr.slice(1).join('$');
              } else {
                selectionName = selectionNames.requiredResponses;
              }
            }

            allFormulae.push({
              name: `${formulaName}` || '',
              mathematicalExpression: `${mathematicalExpression}` || '',
              selectionRule: `${selectionRule}` || '',
              createdDateTime: new Date(),
              mathematicalExpressionError: false,
              selectionRuleError: false,
              selectionName: selectionName,
              arithmetic: arithmetic,
              subSetCount: subSetCount,
            });
          });

        const scoreData = {
          name: `${scoreName}` || 'NewVariable',
          variableNameError: false,
          selected: false,
          formulas: allFormulae,
          createdDateTime: new Date(),
          id: `${scoreId}` || uuidv4(),
        };
        parsedState.push(scoreData);
      }
    });
  }
  return parsedState;
};

export const createSelectionRule = (formula) => {
  if (formula.selectionName == 'Arithmetic Comparison') {
    return formula.selectionRule;
  } else {
    return formula.selectionRule;
  }
};

export const createScoringExtensions = (variables) => {
  const formattedExtensions = variables.map((variable, index) => {
    const formulaExtensions = variable.formulas.map((formula, formulaIndex) => {
      const formulaObj = {
        extension: [
          {
            url: 'formula-name',
            valueString: `${variable.name}-F${formulaIndex + 1}`,
          },
          {
            url: 'mathematical-expression',
            valueString: formula.mathematicalExpression,
          },
          {
            url: 'selection-rule',
            valueString: createSelectionRule(formula),
          },
        ],
        url: 'set-of-api-formula',
      };
      return formulaObj;
    });

    const extensionObj = {
      url: 'list-of-score-definitions',
      extension: [
        {
          url: 'score-id',
          valueCode: variable.id,
        },
        {
          url: 'score-sequence',
          valueInteger: index,
        },
        {
          url: 'score-name',
          valueString: variable.name,
        },
        {
          url: 'list-of-formula-definitions',
          extension: formulaExtensions,
        },
      ],
    };
    return extensionObj;
  });
  return formattedExtensions;
};

export const deepCompareObjects = (obj1, obj2) => {
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
    return obj1 === obj2; // If not objects, compare directly
  }

  if (obj1 === null && obj2 === null) {
    return true;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (!deepCompareObjects(obj1[key], obj2[key])) {
      return false;
    }
  }

  return true;
};
