import React, { useContext, useMemo, useState, useEffect } from 'react';
import { IconButton, Stack, TextField, Typography, FormHelperText, FormControl, FormLabel } from '@mui/material';
import { Close } from '@mui/icons-material';
import { characterLength } from '@/containers/CommonConstants';
import { ValidationErrorContext } from '@/context';
import { extractExtension } from '@/utility/utils';
import { placeholders, strings } from '@/utility/strings';

export const Description = (props) => {
  const { question, handleDescriptionCallback, descriptionExtension, handleDisableDescription } = props;
  const { questionErrors } = useContext(ValidationErrorContext);
  const error = useMemo(() => questionErrors[question.linkId], [questionErrors]);

  const descriptionExtensionValue = extractExtension(descriptionExtension, 'Item/description')?.valueString;
  const [description, setDescription] = useState(question?.description || '');

  useEffect(() => {
    const descriptionExtensionValue = question.extension.find((obj) => obj.url === 'Item/description');
    if (descriptionExtensionValue) {
      descriptionExtensionValue.valueString = description;
    }
  }, [question]);

  const handleOnChange = (event) => {
    // const extension = {
    //   url: 'Item/description',
    //   valueString: event.target.value,
    // };

    // setDescription(event.target.value);
    // handleDescriptionCallback(extension, event.target.value);
    const newText = event.target.value.slice(0, characterLength.descriptionAndExplanationLength);
    const extension = {
      url: 'Item/description',
      valueString: newText,
    };

    setDescription(newText);
    handleDescriptionCallback(extension, newText);
  };
  const isLimitReached = description.length >= characterLength.descriptionAndExplanationLength;

  const handleRemoveDescription = () => {
    setDescription('');
    handleDisableDescription();
  };

  return (
    <>
      <Typography mb={1}>
        <FormLabel required={true} sx={{ color: '#000' }}>
          {strings.description}
        </FormLabel>
      </Typography>
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
        <FormControl fullWidth>
          <TextField
            value={description || ''}
            onChange={handleOnChange}
            placeholder={placeholders.description}
            error={error && !!error.description}
            helperText={error && error.description}
            inputProps={{ maxLength: characterLength.descriptionAndExplanationLength }}
          />
          {isLimitReached && (
            <FormHelperText
              sx={{ textAlign: 'right', color: '#bdc1cc' }}
            >{`${characterLength.descriptionAndExplanationLength} / ${characterLength.descriptionAndExplanationLength}`}</FormHelperText>
          )}
        </FormControl>
        <IconButton onClick={handleRemoveDescription} sx={{ pr: 0 }}>
          <Close sx={{ fontSize: '20px', cursor: 'pointer' }} />
        </IconButton>
      </Stack>
    </>
  );
};
