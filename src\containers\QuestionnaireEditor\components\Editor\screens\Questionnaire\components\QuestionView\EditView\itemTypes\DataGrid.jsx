import React, { useContext, useMemo, useState } from 'react';
import {
  Button,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
  FormLabel,
} from '@mui/material';
import { QuestionTextInput } from './components/QuestionTextInput';
import { Add, Close } from '@mui/icons-material';
import { characterLength } from '@/containers/CommonConstants';
import { placeholders, strings } from '@/utility/strings';
import { ValidationErrorContext } from '@/context';
import { generateShortUuid } from '@/utility/utils';

const columnTypes = [
  { value: 'text', display: 'Text' },
  { value: 'decimal', display: 'Numeric' },
  { value: 'dateTime', display: 'Date' },
];

export const DataGrid = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;
  const { questionErrors } = useContext(ValidationErrorContext);
  const error = useMemo(() => questionErrors[question.linkId], [questionErrors]);

  const [dataGridColumns, setDataGridColumns] = useState(
    question?.item[0] || [
      {
        id: generateShortUuid(),
        type: 'group',
        item: [
          {
            id: generateShortUuid(),
            linkId: generateShortUuid(),
            text: '',
            type: '',
            extension: [
              {
                url: 'Item/complex-value-attribute-sequence',
                valueInteger: 1,
              },
              {
                url: 'Item/complex-value-attribute-type',
                valueInteger: 1,
              },
            ],
          },
        ],
      },
    ],
  );
  const [questionText, setQuestionText] = useState(question?.text || '');

  const handleQuestionCreation = (question) => {
    handleCreateQuestion(pageIndex, questionIndex, { ...question, type: 'group' });
  };

  const handleQuestionTextChange = (event) => {
    // setQuestionText(event.target.value);
    const newText = event.target.value.slice(0, characterLength.itemTextLength);
    setQuestionText(newText);
    let newQuestion = {
      ...question,
      text: event.target.value,
    };

    handleQuestionCreation(newQuestion);
  };

  const handleDataGridColumnTypeDropdown = (event, index) => {
    setDataGridColumns((prevValues) => {
      const newValues = prevValues.item.map((item, i) => {
        if (i === index) {
          return { ...item, type: event.target.value.value };
        }
        return item;
      });

      const newQuestion = {
        ...question,
        item: [{ ...prevValues, item: newValues }],
      };

      handleQuestionCreation(newQuestion);
      return { ...prevValues, item: newValues };
    });
  };

  const handleDataGridColumnLabelChange = (event, index) => {
    setDataGridColumns((prevValues) => {
      const newValues = prevValues.item.map((item, i) => {
        if (i === index) {
          return { ...item, text: event.target.value };
        }
        return item;
      });

      const newQuestion = {
        ...question,
        item: [
          {
            ...prevValues,
            item: newValues,
          },
        ],
      };

      handleQuestionCreation(newQuestion);
      return { ...prevValues, item: newValues };
    });
  };

  const handleAddColumn = () => {
    setDataGridColumns((prevValues) => {
      const newColumn = {
        id: generateShortUuid(),
        linkId: generateShortUuid(),
        text: '',
        type: '',
        extension: [
          {
            url: 'Item/complex-value-attribute-sequence',
            valueInteger: (prevValues.item[prevValues.item.length - 1]?.extension[0]?.valueInteger || 0) + 1,
          },
        ],
      };

      const newItems = [...prevValues.item, newColumn];

      const newQuestion = {
        ...question,
        item: [{ ...prevValues, item: newItems }],
      };

      handleQuestionCreation(newQuestion);

      return { ...prevValues, item: newItems };
    });
  };

  const handleDeleteColumn = (index) => {
    setDataGridColumns((prevValues) => {
      const newItems = prevValues.item.filter((_, i) => i !== index); // Filter out the column at the specified index

      const newQuestion = {
        ...question,
        item: [{ ...prevValues, item: newItems }],
      };

      handleQuestionCreation(newQuestion);
      return { ...prevValues, item: newItems }; // Return the updated prevValues
    });
  };
  return (
    <>
      <QuestionTextInput
        value={questionText}
        onChange={handleQuestionTextChange}
        question={question}
        characterLimit={characterLength.itemTextLength}
      />
      {DescriptionComponent}
      <Stack spacing={2} sx={{ mt: 3 }}>
        {dataGridColumns?.item.map((dataGridColumn, index) => (
          <Stack spacing={1} key={index}>
            <Grid container>
              <Grid item xs={11}>
                <Typography>
                  <FormLabel required={true} sx={{ color: '#000' }}>
                    {`${strings.column} ${index + 1}`}
                  </FormLabel>
                </Typography>
              </Grid>
              <Grid item xs={1} textAlign="right">
                <Close sx={{ fontSize: '20px', cursor: 'pointer' }} onClick={() => handleDeleteColumn(index)} />
              </Grid>
            </Grid>
            <FormControl>
              <TextField
                value={dataGridColumn.text}
                onChange={(event) => handleDataGridColumnLabelChange(event, index)}
                placeholder={placeholders.columnLabel}
                error={error && !!error[`item[0].item[${index}].text`]}
                helperText={error && error[`item[0].item[${index}].text`]}
                inputProps={{ maxLength: characterLength.labelLength }}
              />
              {dataGridColumn?.text.length >= characterLength.labelLength && (
                <FormHelperText
                  sx={{ textAlign: 'right', color: '#bdc1cc' }}
                >{`${characterLength.labelLength} / ${characterLength.labelLength}`}</FormHelperText>
              )}
            </FormControl>
            <FormControl size="small" sx={{ width: { xs: '100%', md: '500px' } }}>
              <InputLabel sx={{ color: 'black' }}>{strings.columnType}</InputLabel>
              <Select
                label={strings.columnType}
                value={dataGridColumn.type}
                onChange={(event) => handleDataGridColumnTypeDropdown(event, index)}
                renderValue={(value) => columnTypes.find((col) => col.value === value)?.display}
                error={error && !!error[`item[0].item[${index}].type`]}
              >
                {columnTypes.map((columnType) => (
                  <MenuItem key={columnType} value={columnType}>
                    {columnType.display}
                  </MenuItem>
                ))}
              </Select>
              {error && !!error[`item[0].item[0].item[${index}].type`] && (
                <FormHelperText error>{error[`item[0].item[${index}].type`]}</FormHelperText>
              )}
            </FormControl>
          </Stack>
        ))}
      </Stack>
      <Button
        variant="text"
        startIcon={<Add fontSize="small" sx={{ cursor: 'pointer' }} />}
        sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' }, mt: 3 }}
        onClick={handleAddColumn}
      >
        {strings.addColumn}
      </Button>
    </>
  );
};
