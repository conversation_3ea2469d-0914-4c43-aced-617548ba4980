import React, { useEffect, useContext } from 'react';
import { Grid, Tooltip, Stack, Paper, Typography, IconButton } from '@mui/material';
import { strings } from '@/utility/strings';
import { tooltipClasses } from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';
import { VariablesAndScoring } from './components/VariablesAndScoring';
import { VariablesAndConditions } from './components/VariablesAndConditions';
import useNotification from '@/hooks/useNotification';
import { ScoringContext } from '@/context';
import { ADD_VARIABLE } from '@/context/scoring';
import { errors } from '@/utility/strings';

export const QuestionnaireScoring = () => {
  const openSnackbar = useNotification();

  const {
    scoringState: variables,
    dispatchScoringAction,
    currentVariable,
    setCurrentVariable,
  } = useContext(ScoringContext);

  const handleCheckForDuplicates = (variables) => {
    const uniqueNames = new Set();

    for (const obj of variables) {
      if (uniqueNames.has(obj.name)) {
        openSnackbar({ variant: 'error', msg: `Duplicate Variable name: ${obj.name} is not allowed.` });
        return false; // Duplicate name found
      }
      uniqueNames.add(obj.name);
    }

    return true;
  };

  const handleAddVariable = () => {
    if (currentVariable.variableNameError) {
      openSnackbar({ variant: 'error', msg: errors.fillRequiredFields });
    }
    const duplicates = handleCheckForDuplicates(variables);
    if (duplicates && !currentVariable.variableNameError) {
      dispatchScoringAction({ type: ADD_VARIABLE });
    }
  };

  useEffect(() => {
    let shallowVariables = [...variables];
    const newVariable = shallowVariables.find((variable) => variable.selected);
    // Use a more predictable identifier, assuming there's an 'id' property
    setCurrentVariable(newVariable);
  }, [variables]);

  const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} placement={props.placement} />
  ))(({ theme, ...props }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: '#f5f5f9',
      color: 'rgba(0, 0, 0, 0.87)',
      fontSize: theme.typography.pxToRem(12),
      border: '1px solid #dadde9',
      minWidth: `${props.size}`,
    },
  }));

  return (
    <>
      <Paper sx={{ border: 'none' }}>
        <Stack direction="flex-start" pb={0.6} alignItems="center">
          <Typography variant="h3">{strings.scoring}</Typography>
        </Stack>
      </Paper>
      <Grid container sx={{ mb: 2 }}>
        <VariablesAndScoring
          HtmlTooltip={HtmlTooltip}
          variables={variables}
          handleAddVariable={handleAddVariable}
          dispatchScoringAction={dispatchScoringAction}
          currentVariable={currentVariable}
          handleCheckForDuplicates={handleCheckForDuplicates}
        />
        <VariablesAndConditions
          HtmlTooltip={HtmlTooltip}
          currentVariable={currentVariable}
          variables={variables}
          setCurrentVariable={setCurrentVariable}
        />
      </Grid>
    </>
  );
};
