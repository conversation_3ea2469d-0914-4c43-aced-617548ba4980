import React, { useMemo } from 'react';
import { Box, FormControlLabel, RadioGroup, Stack, styled, Radio, TextField } from '@mui/material';
import { placeholders, strings } from '@/utility/strings';
import { extractExtension, extractOtherOptionDetails } from '@/utility/utils';
import { QuestionText } from '../components/QuestionText';

const StyledFormControlLabel = styled((props) => <FormControlLabel {...props} />)(({ theme, checked }) => ({
  '.MuiFormControlLabel-label': checked && {
    color: theme.palette.primary.main,
  },
}));

function MyFormControlLabel(props) {
  return <StyledFormControlLabel checked={false} {...props} />;
}

export const RadioQuestion = (props) => {
  const { question, DescriptionComponent } = props;
  const isHorizontal = useMemo(() => {
    const horizontalOrientationExtension = question.extension.find(
      (extension) => extension.url === 'Item/horizontal-orientation',
    );
    return horizontalOrientationExtension ? horizontalOrientationExtension.valueBoolean : false;
  }, [question.extension]);

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}

        <RadioGroup sx={{ pointerEvents: 'none' }}>
          <Stack gap={1} direction={isHorizontal ? 'row' : 'column'} flexWrap="wrap">
            {question?.answerOption.map((item, index) => {
              const otherOptionExtension = extractExtension(
                item.valueCoding.extension,
                'Item/AnswerOption/ValueCoding/other-option',
              );
              const otherOptionAvailable = otherOptionExtension ? otherOptionExtension.valueString : '';
              const { otherOptionId, otherOptionValue } = extractOtherOptionDetails(item.valueCoding.extension);

              return (
                <Stack
                  key={index}
                  direction={{ xs: 'column', sm: 'row' }}
                  alignItems={{ xs: 'flex-start', sm: 'center' }}
                >
                  <MyFormControlLabel
                    value={item.valueCoding.display}
                    control={<Radio />}
                    label={item.valueCoding.display}
                  />

                  {otherOptionAvailable && otherOptionId && item.valueCoding.id === Number(otherOptionId) && (
                    <TextField type="text" autoComplete="off" placeholder={placeholders.otherResponse} />
                  )}
                </Stack>
              );
            })}
          </Stack>
        </RadioGroup>
      </Stack>
    </Box>
  );
};
