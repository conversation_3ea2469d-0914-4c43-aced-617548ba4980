import React, { useContext } from 'react';
import {
  Button,
  ButtonGroup,
  Checkbox,
  FormControl,
  Grid,
  IconButton,
  InputBase,
  Radio,
  Stack,
  TextField,
  Typography,
  styled,
  FormHelperText,
  FormLabel,
} from '@mui/material';
import { Add, Close, RemoveOutlined } from '@mui/icons-material';
import { ValidationErrorContext } from '@/context';
import { strings, placeholders } from '@/utility/strings';
import { LARGE_BUTTON_COLORS, characterLength } from '@/containers/CommonConstants';

const ScoreTextField = styled(InputBase)({
  fontSize: 'inherit',
  width: 60,
  height: 40,
  padding: '4px 0 0 0',
  border: 'solid 1px #ccc',
});

const getLargeButtonOptionStyle = (index) => {
  return {
    fontSize: 10,
    width: 25,
    height: 25,
    borderRadius: '100%',
    // padding: '5px 3px 3px',
    px: 1,
    backgroundColor: Object.values(LARGE_BUTTON_COLORS)[index],
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: 'white',
  };
};

export const MultipleResponseOptions = (props) => {
  const { question, type, answerOption, handleAnswerOptionCallback, handleRemoveAnswerOption } = props;
  const { questionErrors } = useContext(ValidationErrorContext);

  const answerOptionError = questionErrors[question.linkId];

  const handleAnswerOptionChange = (keyName, keyValue, index) => {
    handleAnswerOptionCallback(keyName, keyValue, index);
  };

  const incrementScore = (currentValue, index) => {
    handleAnswerOptionChange('code', currentValue + 1, index);
  };
  const decrementScore = (currentValue, index) => {
    handleAnswerOptionChange('code', currentValue - 1, index);
  };

  return (
    <>
      <Grid container columnSpacing={2} sx={{ mt: 2 }}>
        <Grid item sm={9.5}>
          <Typography>
            <FormLabel required={true} sx={{ color: '#000' }}>
              {strings.responseOptions}
            </FormLabel>
          </Typography>
        </Grid>
        <Grid item sm={2} sx={{ textAlign: 'center' }}>
          <Typography>{strings.score}</Typography>
        </Grid>
      </Grid>
      {answerOption.map((option, optionIndex) =>
        type === 'largeButton' ? (
          <Grid key={optionIndex} container columnSpacing={2} sx={{ mt: 1.5 }}>
            <Grid item sm={9.5} gap={1} sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <FormControl fullWidth>
                <TextField
                  name="display"
                  value={option.valueCoding.display}
                  onChange={(event) =>
                    handleAnswerOptionChange(event.target.name, event.target.value.slice(0, 255), optionIndex)
                  }
                  error={answerOptionError && !!answerOptionError[`answerOption[${optionIndex}].valueCoding.display`]}
                  helperText={
                    answerOptionError && answerOptionError[`answerOption[${optionIndex}].valueCoding.display`]
                  }
                  placeholder={placeholders.responseOption}
                  inputProps={{ maxLength: characterLength.responseOptionLength }}
                />
                {option.valueCoding.display.length >= characterLength.responseOptionLength && (
                  <FormHelperText sx={{ textAlign: 'right', color: '#bdc1cc' }}>
                    {`${characterLength.responseOptionLength}/${characterLength.responseOptionLength}`}
                  </FormHelperText>
                )}
              </FormControl>
              <Stack justifyContent="center" sx={{ height: 40 }}>
                <Typography component="span" sx={getLargeButtonOptionStyle(optionIndex)}>
                  Aa
                </Typography>
              </Stack>
            </Grid>
            <Grid item sm={2} sx={{ textAlign: 'center' }}>
              <ButtonGroup size="small">
                <Button
                  sx={{
                    width: 30,
                    height: 40,
                    borderColor: '#ccc',
                    backgroundColor: '#f0f0f0',
                    '&:hover': { borderColor: '#ccc', borderRightColor: 'transparent !important' },
                  }}
                  onClick={() => decrementScore(option?.valueCoding?.code, optionIndex)}
                >
                  <RemoveOutlined sx={{ fontSize: 14, color: '#3c3c3c' }} />
                </Button>
                <ScoreTextField
                  name="code"
                  inputProps={{ style: { textAlign: 'center' } }}
                  value={option?.valueCoding?.code}
                  onChange={(event) =>
                    handleAnswerOptionChange(event.target.name, Number(event.target.value), optionIndex)
                  }
                />
                <Button
                  sx={{
                    width: 30,
                    height: 40,
                    borderColor: '#ccc',
                    backgroundColor: '#f0f0f0',
                    '&:hover': { borderColor: '#ccc' },
                  }}
                  onClick={() => incrementScore(option?.valueCoding?.code, optionIndex)}
                >
                  <Add sx={{ fontSize: 14, color: '#3c3c3c' }} />
                </Button>
              </ButtonGroup>
            </Grid>
            <Grid
              item
              sm={0.5}
              sx={{
                maxHeight: 40,
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-end',
              }}
            >
              {answerOption?.length > 1 && (
                <IconButton onClick={() => handleRemoveAnswerOption(optionIndex)} sx={{ pr: 0 }}>
                  <Close sx={{ fontSize: '20px' }} />
                </IconButton>
              )}
            </Grid>
          </Grid>
        ) : (
          <Grid key={optionIndex} container columnSpacing={2} sx={{ mt: 1.5 }}>
            <Grid item sm={9.5} sx={{ display: 'flex', alignItems: 'flex-start' }}>
              {type === 'checkbox' ? (
                <Checkbox size="small" sx={{ color: '#999' }} />
              ) : type === 'radio' ? (
                <Radio size="small" sx={{ color: '#999' }} />
              ) : (
                <></>
              )}
              <FormControl fullWidth>
                <TextField
                  name="display"
                  value={option.valueCoding.display}
                  onChange={(event) =>
                    handleAnswerOptionChange(event.target.name, event.target.value.slice(0, 255), optionIndex)
                  }
                  error={answerOptionError && !!answerOptionError[`answerOption[${optionIndex}].valueCoding.display`]}
                  helperText={
                    answerOptionError && answerOptionError[`answerOption[${optionIndex}].valueCoding.display`]
                  }
                  placeholder={
                    option?.valueCoding?.extension?.find(
                      (extension) => extension.url === 'Item/AnswerOption/ValueCoding/other-option',
                    )?.valueString
                      ? placeholders.otherResponseOption
                      : placeholders.responseOption
                  }
                  inputProps={{ maxLength: characterLength.responseOptionLength }}
                />
                {option.valueCoding.display.length >= characterLength.responseOptionLength && (
                  <FormHelperText
                    sx={{ textAlign: 'right', color: '#bdc1cc' }}
                  >{`${characterLength.responseOptionLength}/${characterLength.responseOptionLength}`}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item sm={2} sx={{ textAlign: 'center' }}>
              <ButtonGroup size="small">
                <Button
                  sx={{
                    width: 30,
                    height: 40,
                    borderColor: '#ccc',
                    backgroundColor: '#f0f0f0',
                    '&:hover': { borderColor: '#ccc', borderRightColor: 'transparent !important' },
                  }}
                  onClick={() => decrementScore(option?.valueCoding?.code, optionIndex)}
                >
                  <RemoveOutlined sx={{ fontSize: 14, color: '#3c3c3c' }} />
                </Button>
                <ScoreTextField
                  name="code"
                  inputProps={{ style: { textAlign: 'center' } }}
                  value={option?.valueCoding?.code}
                  onChange={(event) =>
                    handleAnswerOptionChange(event.target.name, Number(event.target.value), optionIndex)
                  }
                />
                <Button
                  sx={{
                    width: 30,
                    height: 40,
                    borderColor: '#ccc',
                    backgroundColor: '#f0f0f0',
                    '&:hover': { borderColor: '#ccc' },
                  }}
                  onClick={() => incrementScore(option?.valueCoding?.code, optionIndex)}
                >
                  <Add sx={{ fontSize: 14, color: '#3c3c3c' }} />
                </Button>
              </ButtonGroup>
            </Grid>
            <Grid
              item
              sm={0.5}
              sx={{
                maxHeight: 40,
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-end',
              }}
            >
              {answerOption?.length > 1 && (
                <IconButton onClick={() => handleRemoveAnswerOption(optionIndex)} sx={{ pr: 0 }}>
                  <Close sx={{ fontSize: '20px' }} />
                </IconButton>
              )}
            </Grid>
          </Grid>
        ),
      )}
    </>
  );
};
