import React, { useContext, useMemo, useState } from 'react';
import { FormControl, FormHelperText, InputLabel, MenuItem, Select, Stack, TextField } from '@mui/material';
import { QuestionTextInput } from './components/QuestionTextInput';
import { placeholders, strings } from '@/utility/strings';
import { characterLength } from '@/containers/CommonConstants';
import { extractExtension, generateShortUuid, mergeTwoExtension } from '@/utility/utils';
import { ValidationErrorContext } from '@/context';

const linearScaleOptions = Array.from({ length: 21 }, (_, i) => i - 10);

export const LinearScale = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;
  const { questionErrors } = useContext(ValidationErrorContext);
  const error = useMemo(() => questionErrors[question.linkId], [questionErrors]);

  const EXTENSION_BASE_URL = 'Item/bar-';
  const possibleExtensionUrls = [`${EXTENSION_BASE_URL}start-label`, `${EXTENSION_BASE_URL}end-label`];

  const { startLabel, endLabel } = useMemo(() => {
    if (question?.extension?.length) {
      const startLabel = extractExtension(question?.extension, EXTENSION_BASE_URL + 'start-label')?.valueString || '';
      const endLabel = extractExtension(question?.extension, EXTENSION_BASE_URL + 'end-label')?.valueString || '';

      return { startLabel, endLabel };
    }
    return {
      startLabel: '',
      endLabel: '',
    };
  }, [question.extension]);

  const [selectedMinValue, setSelectedMinValue] = useState(question?.answerOption[0]?.valueCoding?.display ?? '');
  const [selectedMaxValue, setSelectedMaxValue] = useState(
    question?.answerOption?.length
      ? question?.answerOption[question?.answerOption?.length - 1]?.valueCoding?.display ?? ''
      : '',
  );

  const [questionText, setQuestionText] = useState(question?.text || '');
  const [linearScaleExtension, setLinearExtensions] = useState(
    question?.extension.filter((extension) => possibleExtensionUrls.includes(extension.url)) || [],
  );
  const [linearConfig, setLinearConfig] = useState({
    'start-label': startLabel || '',
    'end-label': endLabel || '',
    'start-value': selectedMinValue,
    'end-value': selectedMaxValue,
  });

  const handleQuestionCreation = (question) => {
    handleCreateQuestion(pageIndex, questionIndex, { ...question, type: 'choice' });
  };

  const handleQuestionTextChange = (event) => {
    // setQuestionText(event.target.value);
    const newText = event.target.value.slice(0, characterLength.itemTextLength);
    setQuestionText(newText);
    let newQuestion = {
      ...question,
      text: event.target.value,
    };
    handleQuestionCreation(newQuestion);
  };

  const handleMinValueDropdown = (e) => {
    setSelectedMinValue(e.target.value);

    // if (selectedMaxValue === null || !(e.target.value < selectedMaxValue)) return;

    const answerOption = [];
    for (let num = e.target.value; num <= selectedMaxValue; num++) {
      answerOption.push({ valueCoding: { code: num, display: num } });
    }
    const newQuestion = { ...question, answerOption };

    let newLinearConfig = { ...linearConfig };
    newLinearConfig['start-value'] = e.target.value;
    setLinearConfig(newLinearConfig);

    handleQuestionCreation({ ...newQuestion, linearConfig: newLinearConfig });
  };

  const handleMaxValueDropdown = (e) => {
    setSelectedMaxValue(e.target.value);

    // if (selectedMinValue === null || selectedMinValue > e.target.value) return;

    const answerOption = [];
    for (let num = selectedMinValue; num <= e.target.value; num++) {
      answerOption.push({ valueCoding: { id: generateShortUuid(), code: num, display: num } });
    }
    const newQuestion = { ...question, answerOption };

    let newLinearConfig = { ...linearConfig };
    newLinearConfig['end-value'] = e.target.value;

    setLinearConfig(newLinearConfig);

    handleQuestionCreation({ ...newQuestion, linearConfig: newLinearConfig });
  };

  const handleLabels = (event) => {
    const { name, value } = event.target || {};

    // let newLinearConfig = { ...linearConfig };
    // linearConfig[name] = value;

    setLinearConfig((prevValue) => {
      const newExtension = linearScaleExtension.map((item) => {
        if (item.url === `${EXTENSION_BASE_URL}${name}`) {
          return {
            ...item,
            valueString: value,
          };
        }
        return item;
      });

      setLinearExtensions(newExtension);

      let newLinearConfig = { ...prevValue };
      newLinearConfig[name] = value;

      handleQuestionCreation({
        ...question,
        linearConfig: newLinearConfig,
        extension: mergeTwoExtension(question.extension, newExtension),
      });
      return newLinearConfig;
    });
    // console.log('value', value, newLinearConfig);
  };

  return (
    <>
      <QuestionTextInput
        value={questionText}
        onChange={handleQuestionTextChange}
        question={question}
        characterLimit={characterLength.itemTextLength}
      />
      {DescriptionComponent}
      <Stack spacing={2} sx={{ mt: 3 }}>
        <Stack direction="row" spacing={2}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel sx={{ color: 'black' }}>{strings.minValue}</InputLabel>
            <Select
              label={strings.minValue}
              value={selectedMinValue}
              onChange={handleMinValueDropdown}
              error={error && !!error['linearConfig.start-value']}
            >
              {linearScaleOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            {error && !!error['linearConfig.start-value'] && (
              <FormHelperText error>{error['linearConfig.start-value']}</FormHelperText>
            )}
          </FormControl>
          <FormControl>
            <TextField
              name="start-label"
              placeholder={placeholders.minValueLabel}
              defaultValue={startLabel}
              onChange={handleLabels}
              error={error && !!error['linearConfig.start-label']}
              helperText={error && error['linearConfig.start-label']}
              inputProps={{ maxLength: characterLength.labelLength }}
            />
            {startLabel.length >= characterLength.labelLength && (
              <FormHelperText
                sx={{ textAlign: 'right', color: '#bdc1cc' }}
              >{`${characterLength.labelLength} / ${characterLength.labelLength}`}</FormHelperText>
            )}
          </FormControl>
        </Stack>
        <Stack direction="row" spacing={2}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel sx={{ color: 'black' }}>{strings.maxValue}</InputLabel>
            <Select
              label={strings.maxValue}
              value={selectedMaxValue}
              onChange={handleMaxValueDropdown}
              error={error && !!error['linearConfig.end-value']}
            >
              {linearScaleOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            {error && !!error['linearConfig.end-value'] && (
              <FormHelperText error>{error['linearConfig.end-value']}</FormHelperText>
            )}
          </FormControl>
          <FormControl>
            <TextField
              name="end-label"
              placeholder={placeholders.maxValueLabel}
              defaultValue={endLabel}
              onChange={handleLabels}
              error={error && !!error['linearConfig.end-label']}
              helperText={error && error['linearConfig.end-label']}
              inputProps={{ maxLength: characterLength.labelLength }}
            />
            {endLabel.length >= characterLength.labelLength && (
              <FormHelperText
                sx={{ textAlign: 'right', color: '#bdc1cc' }}
              >{`${characterLength.labelLength} / ${characterLength.labelLength}`}</FormHelperText>
            )}
          </FormControl>
        </Stack>
      </Stack>
    </>
  );
};
