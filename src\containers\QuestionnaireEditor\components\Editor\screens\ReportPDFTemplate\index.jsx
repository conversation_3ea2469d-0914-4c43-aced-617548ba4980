import React, { useContext, useState } from 'react';
import { Close, Upload } from '@mui/icons-material';
import { Button, IconButton, Paper, Stack, Typography } from '@mui/material';
import { CambianTooltip } from '@/components';
import { strings } from '@/utility/strings';
import { QuestionnaireReportContext, QuestionnaireDetailsContext } from '@/context';

export const ReportPDFTemplate = (props) => {
  const { questionnaireReport, handleQuestionnaireReportContext } = useContext(QuestionnaireReportContext);
  const { questionnaireDetails } = useContext(QuestionnaireDetailsContext);
  const [file, setFile] = useState(
    questionnaireReport?.reportPdfTemplate || {
      fileName: '',
      fileUrl: '',
      fileBase64: '',
      type: '',
      size: '',
    },
  );

  const handleFileChange = async (e) => {
    const file = e.target.files[0];

    if (file) {
      const pdfBase64 = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]); // Remove the prefix
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
      const fileDetails = {
        fileName: file.name,
        fileUrl: URL.createObjectURL(file),
        fileBase64: pdfBase64,
        type: file.type,
        size: file.size,
      };
      handleQuestionnaireReportContext({ reportPdfTemplate: fileDetails });
      setFile(fileDetails);
    }
  };

  const handleFileRemove = () => {
    const initialValue = {
      fileName: '',
      fileUrl: '',
      fileBase64: '',
      type: '',
      size: '',
    };
    handleQuestionnaireReportContext({ reportPdfTemplate: initialValue });
    setFile(initialValue);
  };

  return (
    <>
      <Paper
        sx={{
          border: 'none',
        }}
      >
        <Stack direction="row" spacing={1} py={0.5} justifyContent="space-between">
          <Stack direction="flex-start" alignItems="center">
            <Typography variant="h3">{strings.pdfReportTemplate}</Typography>
          </Stack>
          {file?.fileName ? (
            <Stack direction="row" spacing={1} alignItems="center">
              <Typography>{file.fileName}</Typography>
              <IconButton onClick={handleFileRemove}>
                <Close sx={{ fontSize: '20px' }} />
              </IconButton>
            </Stack>
          ) : (
            <Button component="label" variant="outlined">
              <Upload fontSize="medium" />
              <input type="file" accept="application/pdf" hidden onChange={handleFileChange} />
            </Button>
          )}
        </Stack>
      </Paper>

      <Paper sx={{ p: 2, mt: 2, minHeight: 482 }}>
        {file?.fileBase64 && (
          <object
            data={`data:application/pdf;base64,${file.fileBase64}`}
            type="application/pdf"
            width="100%"
            height="638px"
          >
            <embed src={`data:application/pdf;base64,${file.fileBase64}`} type="application/pdf" />
          </object>
        )}
      </Paper>
    </>
  );
};
