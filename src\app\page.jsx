'use client';
import { useState, useEffect } from 'react';
import { QuestionnaireEditorKit } from '@/containers';
import { questionnaireDetailList, questionnaireList as sampleQuestionnaireList } from './data/questionnaires';
import { v4 as uuidV4 } from 'uuid';
import { generateShortUuid } from '@/utility/utils';
import { htmlDefaultTemplate } from '@/utility/data';
import { headerTemplate, questionnaireDetailsTemplate } from '@/utility/codeBookUtil';
import { Box } from '@mui/material';

const codebookDefaultTemplate = headerTemplate + questionnaireDetailsTemplate;
const downloadFileInJsonFormat = (jsonString, fileName) => {
  const blob = new Blob([jsonString], { type: 'application/json' });
  const href = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = href;
  link.download = fileName + '.json';
  document.body.appendChild(link);
  link.click();

  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};

const authData = {
  IdToken: `
  *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  `,
};

const mergeAndSortQuestionnaires = (draftQuestionnaires, publishedQuestionnaires, key) => {
  let mergedQuestionnaires = [];

  if (!draftQuestionnaires && !publishedQuestionnaires) return mergedQuestionnaires;
  mergedQuestionnaires = mergedQuestionnaires.concat(draftQuestionnaires);
  mergedQuestionnaires = mergedQuestionnaires.concat(publishedQuestionnaires);

  mergedQuestionnaires.sort(function (a, b) {
    if (b[key] < a[key]) {
      return -1;
    } else if (a[key] > b[key]) {
      return 1;
    } else {
      return 0;
    }
  });

  return mergedQuestionnaires;
};

const headers = { Authorization: authData?.IdToken };

export default function Home() {
  const [draftQuestionnaires, setDraftQuestionnaires] = useState([]);
  const [publishedQuestionnaires, setPublishedQuestionnaires] = useState([]);
  const [questionnaireList, setQuestionnaireList] = useState([]);

  useEffect(() => {
    if (draftQuestionnaires || publishedQuestionnaires) {
      const sortKey = 'name';
      const questionnaires = mergeAndSortQuestionnaires(draftQuestionnaires, publishedQuestionnaires, sortKey);
      setQuestionnaireList(questionnaires);
    }
  }, [draftQuestionnaires, publishedQuestionnaires]);

  const getDraftQuestionnaires = async () => {
    try {
      const url =
        'https://2vxcrjyr22.execute-api.ca-central-1.amazonaws.com/Prod/draft-artifact/organization/6ae77cdb-a71c-4753-a8d4-b8f6fa632f53/questionnaires';
      const response = await fetch(url, { headers });
      const draftQuestionnaireResponse = (await response.json()) || [];

      const draftQuestionnaires =
        draftQuestionnaireResponse && draftQuestionnaireResponse?.length
          ? draftQuestionnaireResponse?.map((questionnaire) => ({
              ...questionnaire,
              status: 'draft',
            }))
          : [];

      setDraftQuestionnaires(draftQuestionnaires);
    } catch (e) {
      console.error('e', e);
    }
  };

  const getPublishedQuestionnaires = async () => {
    try {
      const url =
        'https://2vxcrjyr22.execute-api.ca-central-1.amazonaws.com/Prod/artifact/organization/6ae77cdb-a71c-4753-a8d4-b8f6fa632f53/questionnaires';
      const response = await fetch(url, { headers });
      const publishedQuestionnairesResponse = (await response.json()) || [];

      const publishedQuestionnaires =
        publishedQuestionnairesResponse && publishedQuestionnairesResponse?.length
          ? publishedQuestionnairesResponse?.map((questionnaire) => ({
              ...questionnaire,
              status: 'published',
            }))
          : [];

      setPublishedQuestionnaires(publishedQuestionnaires);
    } catch (e) {
      console.error('e', e);
    }
  };

  const getAllQuestionnaires = () => {
    getDraftQuestionnaires();
    getPublishedQuestionnaires();
  };

  // useEffect(() => {
  //   (async () => {
  //     if (authData?.IdToken) {
  //       getAllQuestionnaires();
  //     }
  //   })();
  // }, []);

  const handleSaveDraft = async (fhirQuestionnaire, pdfTemplate) => {
    let newFhirQuestionnaire = structuredClone(fhirQuestionnaire);
    newFhirQuestionnaire.publisher = 'Cambian';
    newFhirQuestionnaire.id = newFhirQuestionnaire.id || uuidV4(); // ideally should be assigned by backend

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('Saved as draft', newFhirQuestionnaire);
        resolve({ success: true, message: '', questionnaire: newFhirQuestionnaire });
      }, 500);
    });
  };

  const handleEditQuestionnaire = async (questionnaireId, questionnaireStatus) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log({ questionnaireId });
        const response = questionnaireDetailList.find(
          (questionnaireMeta) => questionnaireMeta.questionnaire.id === questionnaireId,
        );
        console.log({ response });
        resolve({
          success: true,
          message: '',
          questionnaire: response?.questionnaire,
          pdfTemplate: response?.pdfTemplate,
        });
      }, 500);
    });
  };

  const handleViewQuestionnaire = async (questionnaireId) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const questionnaire = questionnaireDetailList.find(
          (questionnaireMeta) => questionnaireMeta.questionnaire.id === questionnaireId,
        );
        resolve({
          success: true,
          message: '',
          questionnaire: questionnaire?.questionnaire,
        });
      }, 500);
    });
  };

  const handlePublish = async (questionnaireId, publishedStatus) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const questionnaire = questionnaireDetailList.find((questionnaire) => questionnaire.id === questionnaireId);
        let newFhirQuestionnaire = structuredClone(questionnaire);
        console.log('Publish Status ', publishedStatus);
        console.log('Published questionnaire', newFhirQuestionnaire);
        resolve({ success: true, message: '' });
        /* 
          if error
          call -> reject({ success: false, message: 'Internal Server Error' });
        */
      }, 500);
    });
  };

  const handleFinalize = async (fhirQuestionnaire, pdfTemplate) => {
    console.log({ pdfTemplate });
    let newFhirQuestionnaire = structuredClone(fhirQuestionnaire);
    newFhirQuestionnaire.publisher = 'Cambian';
    newFhirQuestionnaire.status = 'final';
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('Finalized questionnaire', newFhirQuestionnaire);
        resolve({ success: true, message: '', questionnaire: newFhirQuestionnaire });
        /* 
          if error
          call -> reject({ success: false, message: 'Internal Server Error' });
        */
      }, 500);
    });
  };

  const handleDelete = (questionnaireId, publishStatus) => {
    console.log('questionnaire to be deleted', questionnaireId, publishStatus);
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('questionnaire deleted');
        resolve({ success: true, message: '' });
        /* 
        if error
        call -> reject({ success: false, message: 'Internal Server Error' });
      */
      }, 500);
    });
  };

  const handlePreview = (questionnaireId, repository) => {
    /* 
      path parameters:
        qid: questionnaire id that needs to be previewed
        repository: questionnaire publish status, possible value 'public' | 'private' | 'both' | 'no'
    */
    /* Note: any dynamic questionnaire widget id can be used by replacing "1" in the url after /questionnaireWidget/  */
    const repo = repository === 'public' ? 'public' : 'private'; // No sense to pass both or no, we fetch from Private anyway.
    const publishStatusParam = `repository=${repo}`;
    const WIDGET_URL = `https://develop.d2yc83ebf3vw6y.amplifyapp.com/widget/organizations/978456c9-7ab5-4a64-80cc-aa11f8a86cbc/questionnaireWidget/1?qid=${questionnaireId}&${publishStatusParam}`;
    window.open(WIDGET_URL, '_blank');
  };

  const handleDuplicate = (questionnaireId) => {
    console.log('received questionnaire id', questionnaireId);
    const questionnaireJson = questionnaireDetailList.find((questionnaire) => questionnaire.id === questionnaireId);
    let duplicatedQuestionnaire = structuredClone(questionnaireJson);
    duplicatedQuestionnaire.id = uuidV4(); // ideally should be assigned by backend
    duplicatedQuestionnaire.status = 'draft';
    for (let i = 0; i < duplicatedQuestionnaire.item.length; i++) {
      const page = duplicatedQuestionnaire.item[i];
      page.id = `group-${generateShortUuid()}`;

      for (let j = 0; j < page.item.length; j++) {
        const question = page.item[j];
        if (question.type === 'group') {
          question.id = `complex-${generateShortUuid()}`;
        } else {
          question.id = generateShortUuid();
        }
      }
    }
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('Duplicated Questionnaire', duplicatedQuestionnaire);
        resolve({ success: true, message: 'Save the questionnaire before duplicating' });
        /* 
        if error
        call -> reject({ success: false, message: 'Internal Server Error' });
      */
      }, 500);
    });
  };

  const handleImport = () => {
    console.log('handle import');
  };

  // TODO: create an example for downloading JSON file
  const handleExport = (questionnaireId, publishStatus) => {
    console.log('exporting questionnaire with id', questionnaireId, publishStatus);
    const questionnaireJson = questionnaireDetailList.find((questionnaire) => questionnaire.id === questionnaireId);
    console.log('export complete, exported json ->', questionnaireJson);
    downloadFileInJsonFormat(JSON.stringify(questionnaireJson), questionnaireJson.name);
  };

  const handleCodebookDownload = (questionnaireId, publishStatus, codebookData) => {
    console.log('Codebook data- ', codebookData);
    console.log('Download Codebook for Id ', questionnaireId, publishStatus);
  };

  return (
    <Box sx={{ p: '24px 4%' }}>
      <QuestionnaireEditorKit
        questionnaireList={sampleQuestionnaireList}
        onEditQuestionnaireCallback={handleEditQuestionnaire}
        onSaveDraftCallback={handleSaveDraft}
        onViewCallback={handleViewQuestionnaire}
        onPublishCallback={handlePublish}
        onDeleteCallback={handleDelete}
        onPreviewCallback={handlePreview}
        onDuplicateCallback={handleDuplicate}
        onImportCallback={handleImport}
        onDownloadCodebookCallback={handleCodebookDownload}
        onExportCallback={handleExport}
        onFinalizeCallback={handleFinalize}
        htmlReportDefaultTemplate={htmlDefaultTemplate}
        codebookDefaultTemplate={codebookDefaultTemplate}
      />
    </Box>
  );
}
