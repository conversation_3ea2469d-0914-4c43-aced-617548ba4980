# Publishing a Component

To Publish the component, do the following steps from the root directory:

1. npm version 0.0.2 --no-git-tag-version
   The version string should be named to a version that is not yet been published

2. npm run rollup
   This command packages up the components into the 'dist' directory, ready to be published

3. npm publish
   Publish the component to the repo.

4. Test the component
   Now that the component has been published to a repo, we recommend that the page(s) used in this project to develop the component is changed
   from using the local import to the import from the component.

NOTE: Should regularly run :
npx browserslist@latest --update-db

The repo is stored at
[cambianRepo](https://github.com/cambianrepo/cambianreact)
and the last published version of this repo can be found at:

[Last Version](https://github.com/cambianrepo/cambianreact/packages/1161792)

# Deletion of the node_modules directory

npm sometimes makes excessively deep "node_modules" directory that cannot be deleted in windows.

To delete these directories:
npm install -g rimraf
rimraf node_modules

https://stackoverflow.com/questions/18875674/whats-the-difference-between-dependencies-devdependencies-and-peerdependencies

# Getting Started

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app). This project has been created to hold
Cambian core components. The project itself can be used as a playground to develop and test a new component. All components developed in this
fashion should be in ./src/lib/components/<ComponentName>/<ComponentName>.jsx. An index file should also be in that directory and the parent index.js
file should export the new component.

The rest of the React app outside of ./src/lib is available to host your new component, both for development and test as well as demonstrating how the
component will look and how to use it.

# Setting up your environment

To clone the repository, you may need to include your github user name. For example:
git clone https://[YOUR_GITHUB_USERNAME]@github.com/cambianrepo/cambianreact.git

You will need to create and set up a personal access token - otherwise you'll not be able to publish or use components in this repo.
You’ll need to get a token. Go to GitHub https://github.com/settings/tokens
And get write:packages / read:packages. Click generate token and save the resulting string.

Go to a terminal window:
npm login --registry=https://npm.pkg.github.com

You’ll be prompted for github username, email and password. Use the token you just generated as your password. This
will add the token and auth configuration to your “.npmrc” file in your user directory.

https://levelup.gitconnected.com/publish-react-components-as-an-npm-package-7a671a2fb7f
https://betterprogramming.pub/build-your-very-own-react-component-library-and-publish-it-to-github-package-registry-192a688a51fd
https://dev.to/alexeagleson/how-to-create-and-publish-a-react-component-library-2oe

Using newly published component:
The component library and all components developed here will be available to be used by running the command:

npm install @<GIT-USER-ID>/<GIT-REPOSITORY>@<VERSION>

ie
npm install @satyam-appscoop/cambianqe@0.0.2

Once imported, components can be imported into a project by:

import { <COMPONENT> } from "@<GIT-USER-ID>/<GIT-REPOSITORY>";

ie
import { QuestionnaireEditor } from "@satyam-appscoop/cambianqe";

The import statement on the page used to test the component should now be changed from something like:

FROM:
import { <COMPONENT> } from "./src/containers";

TO:
import { <COMPONENT> } from "@<GIT-USER-ID>/<GIT-REPOSITORY>";

For testing, create a page in src/app/page_name/page.jsx (page_name can put as component name) and import the component there.
The page used to test the component should still work exactly as it worked while developing the component locally, but by drawing in the
component remotely from the repo. If all is still good that component is now ready to be used as a component import elsewhere.

Note: All dependencies needed to use the component should be expressed in either the peerDependencies or devDependencies - but not dependencies.

#Project Details

This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, install all the dependencies

```bash
npm install
# or
yarn install
```

Second, run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:5005](http://localhost:5005) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
