import React, { useEffect, useRef } from 'react';

export const useDebouncing = () => {
  const timeoutRef = useRef();

  // Cleanup function
  useEffect(() => {
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, []);

  const debounceFunction = (func, delay, ...args) => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => func(...args), delay);
  };

  return { debounceFunction };
};
