import { addKeysToScoringItems } from '@/utility/utils';
import React, { createContext, useEffect, useReducer, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
// Define the initial state of your context

export const ADD_VARIABLE = 'ADD_VARIABLE';
export const ADD_EQUATION = 'ADD_EQUATION';
export const ADD_ARITHMETIC = 'ADD_ARITHMETIC';
export const SELECT_EQUATION = 'SELECT_EQUATION';
export const DELETE_VARIABLE = 'DELETE_VARIABLE';
export const ADD_CONDITION = 'ADD_CONDITION';
export const ADD_VARIABLE_NAME = 'ADD_VARIABLE_NAME';
export const SELECT_VARIABLE = 'SELECT_VARIABLE';
export const ADD_ITEM_NAME = 'ADD_ITEM_NAME';
export const ADD_ITEM_NAME_EQUATION = 'ADD_ITEM_NAME_EQUATION';
export const SELECT_ITEM_REQUIRED_RESPONSES = 'SELECT_ITEM_REQUIRED_RESPONSES';
export const ADD_LEAST_SELECTED_NUMBER = 'ADD_LEAST_SELECTED_NUMBER';
export const DELETE_CONDITION = 'DELETE_CONDITION';
export const CHECK_EMPTY = 'CHECK_EMPTY';
export const REINITIALIZE_SCORING = 'REINITIALIZE_SCORING';

const scoringStateStructure = [
  {
    name: 'NewVariable',
    variableNameError: false,
    selected: true,
    formulas: [
      {
        createdDateTime: new Date(),
        mathematicalExpression: '',
        mathematicalExpressionError: false,
        name: '',
        selectionRule: 'Select Rule',
        selectionRuleError: false,
        selectionName: 'Select Rule',
        arithmetic: '',
        subSetCount: 0,
      },
    ],
    createdDateTime: new Date(),
    id: uuidv4(),
  },
];

const handleAddVariable = (variables) => {
  let addVariable = true;
  let updatedVariables = structuredClone(variables);

  updatedVariables.forEach((variable) => {
    if (variable.selected) {
      variable.formulas.forEach((formula) => {
        if (formula.selectionRule == 'Select Rule') {
          formula.selectionRuleError = true;
          addVariable = false;
        } else {
          formula.selectionRuleError = false;
          addVariable = true;
        }

        if (formula.mathematicalExpression == '') {
          formula.mathematicalExpressionError = true;
          addVariable = false;
        } else {
          formula.mathematicalExpressionError = false;
          addVariable = true;
        }
      });
    }
  });

  if (addVariable == true) {
    updatedVariables.forEach((variable) => (variable.selected = false));

    return [
      ...updatedVariables,
      {
        name: 'NewVariable',
        variableNameError: false,
        selected: true,
        formulas: [
          {
            createdDateTime: new Date(),
            mathematicalExpression: '',
            mathematicalExpressionError: false,
            name: '',
            selectionRule: 'Select Rule',
            selectionRuleError: false,
            selectionName: 'Select Rule',
            arithmetic: '',
            subSetCount: 0,
          },
        ],
        createdDateTime: new Date(),
        id: uuidv4(),
      },
    ];
  } else {
    return updatedVariables;
  }
};

const handleEquationChange = (variables, key, value) => {
  let updatedVariables = structuredClone(variables);
  updatedVariables.forEach((variable) => {
    if (variable.selected) {
      variable.formulas[key].mathematicalExpression = value;
      variable.formulas[key].mathematicalExpressionError = false;
    }
  });
  return updatedVariables;
};

const handleQuestionRuleDropdown = (variables, key, value, items) => {
  let updatedVariables = structuredClone(variables);
  updatedVariables.forEach((variable) => {
    if (variable.selected) {
      if (value === 'Arithmetic Comparison') {
        // variable.formulas[key].selectionRule = 'VAR_COMPARE$';
        const arithmeticValue = variable.formulas[key].arithmetic || ''; // Get the arithmetic value
        variable.formulas[key].selectionRule = `VAR_COMPARE$${arithmeticValue}`;
        variable.formulas[key].selectionName = 'Arithmetic Comparison';
      } else if (value === 'Required Responses') {
        let itemLength = 0;
        {
          items.forEach((itemArr) => {
            itemLength = itemLength + itemArr.item.length;
          });
        }
        variable.formulas[key].selectionRule = 'X'.repeat(itemLength);
        variable.formulas[key].selectionName = 'Required Responses';
      } else if (value === 'Required responses count from subset') {
        let itemLength = 0;
        {
          items.forEach((itemArr) => {
            itemLength = itemLength + itemArr.item.length;
          });
        }
        variable.formulas[key].selectionRule = `NUM_Q_EQ$0$${'X'.repeat(itemLength)}`;
        variable.formulas[key].selectionName = 'Required responses count from subset';
      }
      variable.formulas[key].selectionRuleError = false;
    }
  });
  return updatedVariables;
};

const handleArithmeticChange = (variables, value, keyVal) => {
  let updatedVariables = structuredClone(variables);
  updatedVariables.forEach((variable) => {
    if (variable.selected) {
      variable.formulas[keyVal].arithmetic = value;
      variable.formulas[keyVal].selectionRule = 'VAR_COMPARE$' + value;
    }
  });
  return updatedVariables;
};

const handleAddCondition = (variables) => {
  return variables.map((variable) => {
    if (variable.selected) {
      return {
        ...variable,
        formulas: [
          ...variable.formulas,
          {
            createdTime: new Date(),
            mathematicalExpression: '',
            mathematicalExpressionError: false,
            name: '',
            selectionRule: 'Select Rule',
            selectionRuleError: false,
            selectionName: 'Select Rule',
            arithmetic: '',
            subSetCount: 0,
          },
        ],
      };
    }
    return variable;
  });
};

// const handleVariableName = (variables, value) => {
//   let updatedVariables = [...variables];

//   const newState = updatedVariables.map((variable) => {
//     if (variable.selected) {
//       variable.name = value;
//     }
//     return variable;
//   });
//   return newState;
// };

const handleVariableName = (variables, value) => {
  let updatedVariables = structuredClone(variables);
  const alphanumericUnderscoreRegex = /^[a-zA-Z0-9_]+$/;
  const isValidVariableName = alphanumericUnderscoreRegex.test(value);

  const newState = updatedVariables.map((variable) => {
    if (variable.selected) {
      if (isValidVariableName) {
        variable.variableNameError = false;
      } else {
        variable.variableNameError = true;
      }
      variable.name = value;
    }
    return variable;
  });

  return newState;
};

const handleVariableClick = (variables, key) => {
  let selectVariable = true;
  let updatedVariables = structuredClone(variables);

  updatedVariables.forEach((variable) => {
    if (variable.selected) {
      if (variable.variableNameError) {
        selectVariable = false;
      } else {
        variable.variableNameError = false;
      }

      variable.formulas.forEach((formula) => {
        if (formula.selectionRule == 'Select Rule') {
          formula.selectionRuleError = true;
          selectVariable = false;
        } else {
          formula.selectionRuleError = false;
        }

        if (formula.mathematicalExpression == '') {
          formula.mathematicalExpressionError = true;
          selectVariable = false;
        } else {
          formula.mathematicalExpressionError = false;
        }
      });
    }
  });

  if (selectVariable) {
    return variables.map((variable, index) => ({
      ...variable,
      selected: key === index,
    }));
  } else {
    return updatedVariables;
  }
};

const handleItemDropArithmetic = (variables, keyVal, item) => {
  return variables.map((variable) => {
    if (variable.selected) {
      return {
        ...variable,
        formulas: variable.formulas.map((formula, index) => {
          if (index === keyVal) {
            return {
              ...formula,
              arithmetic: formula.arithmetic + `#{${item.text}}`,
              selectionRule: formula.selectionRule + `#{${item.text}}`,
            };
          }
          return formula;
        }),
      };
    }
    return variable;
  });
};

const handleItemDropEquation = (variables, keyVal, item) => {
  return variables.map((variable) => {
    if (variable.selected) {
      return {
        ...variable,
        formulas: variable.formulas.map((formula, index) => {
          if (index === keyVal) {
            return {
              ...formula,
              mathematicalExpression: formula.mathematicalExpression + `#{${item.text}}`,
              mathematicalExpressionError: false,
            };
          }
          return formula;
        }),
      };
    }
    return variable;
  });
};

const handleRequiredResponsesFormChange = (variables, value, keyVal, selectionRule) => {
  console.log(variables, value, keyVal, selectionRule);
  if (selectionRule === 'Required responses count from subset') {
    const order = value;
    let updatedVariables = structuredClone(variables);

    if (order === '-1') {
      const newState = updatedVariables.map((variable) => {
        if (variable.selected) {
          return {
            ...variable,
            formulas: variable.formulas.map((formula, index) => {
              if (index === keyVal) {
                let chars = formula.selectionRule.split('');
                let selectionRule = formula.selectionRule;
                for (let i = 11; i < selectionRule.length; i++) {
                  chars[i] = chars[i] === 'X' ? '1' : 'X';
                }
                return {
                  ...formula,
                  selectionRule: chars.join(''),
                };
              }
              return formula;
            }),
          };
        }
        return variable;
      });
      return newState;
    } else {
      const newState = updatedVariables.map((variable) => {
        if (variable.selected) {
          return {
            ...variable,
            formulas: variable.formulas.map((formula, index) => {
              if (index === keyVal) {
                let chars = formula.selectionRule.split('');
                // The 11 is to offset the initial text which is NUM_Q_EQ$0$, and to only update the X values in the end
                if (chars[Number(order) + 11] === 'X') {
                  chars[Number(order) + 11] = '1';
                } else {
                  chars[Number(order) + 11] = 'X';
                }
                return {
                  ...formula,
                  selectionRule: chars.join(''),
                };
              }
              return formula;
            }),
          };
        }
        return variable;
      });
      return newState;
    }
  } else {
    const order = value;
    let updatedVariables = structuredClone(variables);
    if (order === '-1') {
      const newState = updatedVariables.map((variable) => {
        if (variable.selected) {
          return {
            ...variable,
            formulas: variable.formulas.map((formula, index) => {
              if (index === keyVal) {
                if (formula.selectionRule.includes('X')) {
                  let replaced = formula.selectionRule.replace(/./g, '1');
                  return {
                    ...formula,
                    selectionRule: replaced,
                  };
                } else {
                  let replaced = formula.selectionRule.replace(/./g, 'X');
                  return {
                    ...formula,
                    selectionRule: replaced,
                  };
                }
              }
              return formula;
            }),
          };
        }
        return variable;
      });
      return newState;
    } else {
      return variables.map((variable) => {
        if (variable.selected) {
          return {
            ...variable,
            formulas: variable.formulas.map((formula, index) => {
              if (index === keyVal) {
                let chars = formula.selectionRule.split('');
                console.log(chars);
                if (chars[order] === 'X') {
                  chars[order] = '1';
                } else {
                  chars[order] = 'X';
                }
                return {
                  ...formula,
                  selectionRule: chars.join(''),
                };
              }
              return formula;
            }),
          };
        }
        return variable;
      });
    }
  }
};

const handleLeastSelectedNumber = (variables, value, keyVal) => {
  let updatedVariables = structuredClone(variables);
  const newState = updatedVariables.map((variable) => {
    if (variable.selected) {
      return {
        ...variable,
        formulas: variable.formulas.map((formula, index) => {
          if (index === keyVal) {
            if (value === '') {
              return {
                ...formula,
                selectionRule: formula.selectionRule.replace(/\$[^$]+\$/, `$0$`),
                subSetCount: value,
              };
            } else {
              return {
                ...formula,
                selectionRule: formula.selectionRule.replace(/\$[^$]+\$/, `$${value}$`),
                subSetCount: value,
              };
            }
          }
          return formula;
        }),
      };
    }
    return variable;
  });
  return newState;
};

const handleRemoveConditionClick = (variables, key) => {
  let updatedVariables = structuredClone(variables);

  updatedVariables = updatedVariables.map((variable) => {
    if (variable.selected) {
      const updatedFormulas = structuredClone(variable.formulas);
      updatedFormulas.splice(key, 1);
      return {
        ...variable,
        formulas: updatedFormulas,
      };
    }
    return variable;
  });
  return updatedVariables;
};

const handleDeleteVariable = (variables) => {
  let updatedVariables = structuredClone(variables);

  let filtered;
  if (updatedVariables.length === 1) {
    filtered = [
      {
        ...updatedVariables[0],
        createdDateTime: new Date(),
        name: 'NewVariable',
        selected: true,
        variableNameError: false,
        formulas: [
          {
            ...updatedVariables[0].formulas[0],
            createdDateTime: new Date(),
            mathematicalExpression: '',
            mathematicalExpressionError: false,
            name: '',
            selectionRule: 'Select Rule',
            selectionRuleError: false,
            selectionName: 'Select Rule',
            arithmetic: '',
            subSetCount: 0,
          },
        ],
      },
    ];
  } else {
    filtered = updatedVariables.filter((variable) => !variable.selected);
  }

  if (filtered.length > 0) {
    filtered[0] = {
      ...filtered[0],
      selected: true,
    };
  }
  return filtered;
};

const handleCheckForEmptyFields = (variables) => {
  let updatedVariables = structuredClone(variables);

  updatedVariables.forEach((variable) => {
    return variable.formulas.forEach((formula) => {
      if (formula.mathematicalExpression.trim() == '') {
        formula.mathematicalExpressionError = true;
      }
      return formula;
    });
  });
  return updatedVariables;
};

// Define a reducer function
const reducer = (state, action) => {
  const { type, key, value, items, item, selectionName, updatedScoringState } = action || {};

  switch (type) {
    case ADD_VARIABLE:
      const newState = handleAddVariable(state);
      return newState;

    case ADD_EQUATION:
      const equationUpdatedState = handleEquationChange(state, key, value);
      return equationUpdatedState;

    case ADD_ARITHMETIC:
      const arithmeticUpdatedState = handleArithmeticChange(state, value, key);
      return arithmeticUpdatedState;

    case SELECT_EQUATION:
      const questionRuleUpdatedState = handleQuestionRuleDropdown(state, key, value, items);
      return questionRuleUpdatedState;

    case ADD_CONDITION:
      const addConditionUpdatedState = handleAddCondition(state);
      return addConditionUpdatedState;

    case ADD_VARIABLE_NAME:
      const addVariableNameUpdatedState = handleVariableName(state, value);
      return addVariableNameUpdatedState;

    case SELECT_VARIABLE:
      const selectVariableUpdatedState = handleVariableClick(state, key);
      return selectVariableUpdatedState;

    case ADD_ITEM_NAME:
      const addedItemNameUpdatedState = handleItemDropArithmetic(state, key, item);
      return addedItemNameUpdatedState;

    case ADD_ITEM_NAME_EQUATION:
      const addedItemNameEquationUpdatedState = handleItemDropEquation(state, key, item);
      return addedItemNameEquationUpdatedState;

    case SELECT_ITEM_REQUIRED_RESPONSES:
      const selectedRequiredResponseUpdatedState = handleRequiredResponsesFormChange(state, value, key, selectionName);
      return selectedRequiredResponseUpdatedState;

    case ADD_LEAST_SELECTED_NUMBER:
      const leastSelectedNumberUpdatedState = handleLeastSelectedNumber(state, value, key);
      return leastSelectedNumberUpdatedState;

    case DELETE_CONDITION:
      const removedConditionUpdatedState = handleRemoveConditionClick(state, key);
      return removedConditionUpdatedState;

    case DELETE_VARIABLE:
      const removedVariableUpdatedState = handleDeleteVariable(state);
      return removedVariableUpdatedState;

    case CHECK_EMPTY:
      const empty = handleCheckForEmptyFields(state);
      return empty;

    case REINITIALIZE_SCORING:
      return updatedScoringState;

    default:
      return state;
  }
};

// Create the context
export const ScoringContext = createContext();

// Create a provider component
export const ScoringProvider = ({ children, existingQuestionnaireData }) => {
  const parsedScoringData = addKeysToScoringItems(existingQuestionnaireData?.extension);
  const [initialState, setInitialState] = useState(
    parsedScoringData.length >= 1 ? parsedScoringData : scoringStateStructure,
  );

  if (initialState && initialState[0]) initialState[0].selected = true;

  const [state, dispatch] = useReducer(reducer, initialState);
  const [currentVariable, setCurrentVariable] = useState(initialState[0]);

  useEffect(() => {
    if (existingQuestionnaireData) {
      const parsedScoringData = addKeysToScoringItems(existingQuestionnaireData?.extension);
      if (parsedScoringData?.length) {
        setInitialState(parsedScoringData);
      }
    }
  }, [existingQuestionnaireData?.extension]);

  return (
    <ScoringContext.Provider
      value={{
        scoringState: state,
        dispatchScoringAction: dispatch,
        currentVariable,
        setCurrentVariable,
        initialScoringState: initialState,
      }}
    >
      {children}
    </ScoringContext.Provider>
  );
};
