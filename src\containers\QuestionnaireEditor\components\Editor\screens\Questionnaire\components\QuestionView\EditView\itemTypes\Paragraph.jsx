import React, { useState } from 'react';
import { QuestionTextInput } from './components/QuestionTextInput';
import { characterLength } from '@/containers/CommonConstants';

export const Paragraph = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;
  const [questionText, setQuestionText] = useState(question?.text || '');

  const handleQuestionTextChange = (event) => {
    // setQuestionText(event.target.value);
    const newText = event.target.value.slice(0, characterLength.itemTextLength);
    setQuestionText(newText);
    let newQuestion = {
      ...question,
      type: 'text',
      text: event.target.value,
    };
    handleCreateQuestion(pageIndex, questionIndex, newQuestion);
  };

  return (
    <>
      <QuestionTextInput
        value={questionText}
        onChange={handleQuestionTextChange}
        question={question}
        characterLimit={characterLength.itemTextLength}
      />
      {DescriptionComponent}
    </>
  );
};
