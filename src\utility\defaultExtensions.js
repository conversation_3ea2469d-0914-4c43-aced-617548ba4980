export const bodyDiagramDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5529,
  },
];

export const checkboxesDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5521,
  },
];

export const dataGridDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5529,
  },
];

export const datePickerDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5529,
  },
];

export const displayQuestionDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5529,
  },
];

export const dropdownDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5522,
  },
];

export const integerDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5520,
  },
  {
    url: 'Item/min-value',
    valueInteger: 0,
  },
  {
    url: 'Item/max-value',
    valueInteger: 100,
  },
];

export const largeButtonDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5523,
  },
];

export const linearScaleDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5524,
  },
];

export const numberDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5520,
  },
  {
    url: 'Item/min-value',
    valueDecimal: 0,
  },
  {
    url: 'Item/max-value',
    valueDecimal: 100,
  },
];

export const numericSliderDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5525,
  },
];

export const paragraphDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5520,
  },
  {
    url: 'Item/min-length',
    valueInteger: 0,
  },
  {
    url: 'Item/max-length',
    valueInteger: 1024,
  },
];

export const radioDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5521,
  },
];

export const textDefaultExtension = [
  {
    url: 'Item/description',
    valueString: null,
  },
  {
    url: 'Item/explanation',
    valueString: null,
  },
  {
    url: 'Item/explanation-flag',
    valueString: null,
  },
  {
    url: 'Item/trendable',
    valueBoolean: false,
  },
  {
    url: 'Item/horizontal-orientation',
    valueBoolean: false,
  },
  {
    url: 'Item/hide-question',
    valueBoolean: false,
  },
  {
    url: 'Item/question-type-id',
    valueInteger: 5520,
  },
  {
    url: 'Item/min-length',
    valueInteger: 0,
  },
  {
    url: 'Item/max-length',
    valueInteger: 255,
  },
];
