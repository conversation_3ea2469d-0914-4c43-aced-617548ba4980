'use client';
import { ThemeProvider } from '@mui/material/styles';
import { CambianTheme } from '@/components';
import { CssBaseline } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { ErrorOutline, InfoOutlined, TaskAlt, WarningAmberOutlined } from '@mui/icons-material';

export default function Wrappers({ children }) {
  const snackbarIconVariants = {
    success: <TaskAlt fontSize="small" />,
    error: <ErrorOutline fontSize="small" />,
    info: <InfoOutlined fontSize="small" />,
    warning: <WarningAmberOutlined fontSize="small" />,
  };

  return (
    <ThemeProvider theme={CambianTheme}>
      <CssBaseline />
      <SnackbarProvider iconVariant={snackbarIconVariants} maxSnack={3}>
        {children}
      </SnackbarProvider>
    </ThemeProvider>
  );
}
