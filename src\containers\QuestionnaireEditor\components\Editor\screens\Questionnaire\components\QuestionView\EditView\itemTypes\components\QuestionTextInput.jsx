import React, { useContext } from 'react';
import { FormControl, TextField, Typography, FormHelperText, FormLabel } from '@mui/material';
import { ValidationErrorContext } from '@/context';
import { placeholders, strings } from '@/utility/strings';

export const QuestionTextInput = (props) => {
  const { question, value, onChange, characterLimit } = props;
  const { questionErrors } = useContext(ValidationErrorContext);

  const error = questionErrors[question.linkId]?.text;
  const isLimitReached = characterLimit - value.length <= 0;

  return (
    <>
      <Typography sx={{ mb: 1 }}>
        <FormLabel required={true} sx={{ color: '#000' }}>
          {strings.item}
        </FormLabel>
      </Typography>
      <FormControl fullWidth>
        <TextField
          value={value}
          onChange={onChange}
          inputProps={{ maxLength: characterLimit }}
          placeholder={placeholders.item}
          error={!!error}
          helperText={error}
        />{' '}
        {isLimitReached && (
          <FormHelperText sx={{ textAlign: 'right', color: '#bdc1cc' }}>
            {`${characterLimit} / ${characterLimit}`}
          </FormHelperText>
        )}
      </FormControl>
    </>
  );
};
