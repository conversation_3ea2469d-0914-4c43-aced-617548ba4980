# QuestionnaireEditorKit Documentation

## Overview

The QuestionnaireEditorKit is a React component designed to facilitate the management and manipulation of questionnaires in the FHIR (Fast Healthcare Interoperability Resources) format. It provides a user interface with various functionalities, such as saving drafts, publishing, deleting, previewing, duplicating, importing, and exporting questionnaires.

## Usage

To integrate the QuestionnaireEditorKit into your React application, follow these steps:

1. Import the component:

   ```javascript
   import { QuestionnaireEditorKit } from '@github_username/cambianqe';
   ```

2. Include the component in your JSX code:

   ```javascript
   <QuestionnaireEditorKit
     questionnaireList={questionnaireList}
     onSaveDraftCallback={handleSaveDraft}
     onPublishCallback={handlePublish}
     onDeleteCallback={handleDelete}
     onPreviewCallback={handlePreview}
     onDuplicateCallback={handleDuplicate}
     onImportCallback={handleImport}
     onExportCallback={handleExport}
   />
   ```

## Callback Functions

The QuestionnaireEditorKit expects several callback functions to manage different actions related to questionnaires. Here are the functions you need to implement:

1. **onSaveDraftCallback(fhirQuestionnaire)**

   - **Description**: Saves a questionnaire as a draft.
   - **Parameters**: `fhirQuestionnaire`: The FHIR-formatted questionnaire to be saved.
   - **Returns**: A Promise that resolves with an object `{ success: true, message: '' }` after a delay of 500ms.

2. **onPublishCallback(fhirQuestionnaire)**

   - **Description**: Publishes a questionnaire.
   - **Parameters**: `fhirQuestionnaire`: The FHIR-formatted questionnaire to be published.
   - **Returns**: A Promise that resolves with an object `{ success: true, message: '' }` after a delay of 500ms. Use reject in case of an error.

3. **onDeleteCallback(questionnaireId)**

   - **Description**: Deletes a questionnaire.
   - **Parameters**: `questionnaireId`: The unique identifier of the questionnaire to be deleted.
   - **Returns**: A Promise that resolves with an object `{ success: true, message: '' }` after a delay of 500ms. Use reject in case of an error.

4. **onPreviewCallback(questionnaireId)**

   - **Description**: Opens a preview of the questionnaire in a new browser window.
   - **Parameters**: `questionnaireId`: The unique identifier of the questionnaire to be previewed.

5. **onDuplicateCallback(questionnaireId)**

   - **Description**: Duplicates a questionnaire.
   - **Parameters**: `questionnaireId`: The unique identifier of the questionnaire to be duplicated.
   - **Returns**: A Promise that resolves with an object `{ success: true, message: '' }` after a delay of 500ms. Use reject in case of an error.

6. **onImportCallback()**

   - **Description**: Handles the import functionality. (No parameters required)

7. **onExportCallback(questionnaireId)**
   - **Description**: Exports a questionnaire in JSON format.
   - **Parameters**: `questionnaireId`: The unique identifier of the questionnaire to be exported.

## Example Implementation

```javascript
import React from 'react';
import QuestionnaireEditorKit from 'path/to/QuestionnaireEditorKit';

const YourComponent = () => {
  // Define your questionnaireList and callback functions here

  return (
    <QuestionnaireEditorKit
      questionnaireList={questionnaireList}
      onSaveDraftCallback={handleSaveDraft}
      onPublishCallback={handlePublish}
      onDeleteCallback={handleDelete}
      onPreviewCallback={handlePreview}
      onDuplicateCallback={handleDuplicate}
      onImportCallback={handleImport}
      onExportCallback={handleExport}
    />
  );
};

export default YourComponent;
```

`For more details and examples, refer to the ./src/app/page.jsx file in your project.`
