import React from 'react';
import { Box, Stack, TextField } from '@mui/material';
import { placeholders, strings } from '@/utility/strings';
import { QuestionText } from '../components/QuestionText';

export const Text = (props) => {
  const { question, DescriptionComponent } = props;

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <TextField placeholder={placeholders.yourResponse} />
      </Stack>
    </Box>
  );
};
