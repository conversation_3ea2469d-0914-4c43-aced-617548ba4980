import React from 'react';
import { codebookTemplateData } from '@/utility/codeBookUtil';
import { Paper, Typography, IconButton } from '@mui/material';
import { Close } from '@mui/icons-material';

export const ShowCodebookTemplateFile = (props) => {
  const { onClose } = props;

  return (
    <>
      <Paper sx={{ p: 2, position: 'relative' }}>
        <IconButton onClick={onClose} style={{ position: 'absolute', top: '10px', right: '15px' }} size="large">
          <Close sx={{ fontSize: '20px' }} />
        </IconButton>
        <Typography dangerouslySetInnerHTML={{ __html: codebookTemplateData }}></Typography>
      </Paper>
    </>
  );
};
