import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import { DisplayView } from './DisplayView';
import { EditView } from './EditView';
import {
  choiceQuestions,
  dateTimeQuestion,
  numericQuestion,
  otherQuestions,
  textQuestions,
} from '@/containers/CommonConstants';
import {
  extractExtension,
  isBodyDiagramQuestion,
  isCheckboxQuestion,
  isDropdownQuestion,
  isIntegerOnlyQuestion,
  isLargeButtonQuestion,
  isLinearScaleQuestion,
  isNumericSliderQuestion,
  isRadioButtonQuestion,
} from '@/utility/utils';

const getSelectedQuestionType = (question) => {
  if (question.type === 'date' || question.type === 'dateTime') {
    return dateTimeQuestion[0];
  } else if (question.type === 'group') {
    return otherQuestions[1];
  } else if (isBodyDiagramQuestion(question)) {
    return otherQuestions[0];
  } else if (isLinearScaleQuestion(question)) {
    return numericQuestion.find((question) => question.type === 'linearScale');
  } else if (isNumericSliderQuestion(question)) {
    return numericQuestion.find((question) => question.type === 'numericSlider');
  } else if (isDropdownQuestion(question)) {
    return choiceQuestions.find((question) => question.type === 'dropdown');
  } else if (isLargeButtonQuestion(question)) {
    return choiceQuestions.find((question) => question.type === 'largeButton');
  } else if (isRadioButtonQuestion(question)) {
    return choiceQuestions.find((question) => question.type === 'radio');
  } else if (isCheckboxQuestion(question)) {
    return choiceQuestions.find((question) => question.type === 'checkboxes');
  } else if (question.type === 'text') {
    const TEXT_QUESTION_ALLOWED_LENGTH = 255;
    const maxAllowedLengthExtension = extractExtension(question.extension, 'Item/max-length');

    return textQuestions.find((question) =>
      maxAllowedLengthExtension?.valueInteger > TEXT_QUESTION_ALLOWED_LENGTH
        ? question.type === 'paragraph'
        : question.type === 'text',
    );
  } else if (question.type === 'integer' && isIntegerOnlyQuestion(question)) {
    return textQuestions.find((question) => question.type === 'integer');
  } else if (question.type === 'decimal') {
    return textQuestions.find((question) => question.type === 'decimal');
  } else if (question.type === 'display') {
    return textQuestions.find((question) => question.type === 'display');
  }
  return { label: '', type: '' };
};

export const QuestionView = (props) => {
  const { pageIndex, questionIndex, question } = props;

  const [selectedQuestionType, setSelectedQuestionType] = useState(getSelectedQuestionType(question));
  useEffect(() => {
    setSelectedQuestionType(getSelectedQuestionType(question));
  }, [question]);

  return (
    <>
      <Box sx={{ mb: 2 }}>
        <Typography fontSize={14} sx={{ color: '#ADADAD' }}>
          {question.linkId}
        </Typography>
      </Box>
      {question?.isEditMode ? (
        <EditView
          pageIndex={pageIndex}
          questionIndex={questionIndex}
          selectedQuestionType={selectedQuestionType}
          setSelectedQuestionType={setSelectedQuestionType}
        />
      ) : (
        <DisplayView
          question={question}
          pageIndex={pageIndex}
          questionIndex={questionIndex}
          selectedQuestionType={selectedQuestionType}
        />
      )}
    </>
  );
};
