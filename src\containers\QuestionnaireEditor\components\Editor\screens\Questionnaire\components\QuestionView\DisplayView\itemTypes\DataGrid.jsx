import React from 'react';
import { <PERSON>, Grid, Stack, TextField, Typography } from '@mui/material';
import { strings, placeholders } from '@/utility/strings';
import { dateTimeFormats } from '@/containers/CommonConstants';
import { QuestionText } from '../components/QuestionText';

const DataGridColumns = (props) => {
  const { column } = props;

  return (
    <Stack>
      <Typography variant="body2">{column.text}</Typography>
      <TextField
        placeholder={column.type === 'dateTime' ? dateTimeFormats.placeholders.date : placeholders.yourResponse}
      />
    </Stack>
  );
};

export const DataGrid = (props) => {
  const { question, DescriptionComponent } = props;

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <Grid container spacing={2}>
          {question?.item[0]?.item?.map((column, index) => (
            <Grid key={index} item xs={3}>
              <DataGridColumns column={column} />
            </Grid>
          ))}
        </Grid>
      </Stack>
    </Box>
  );
};
