import React, { useContext, useMemo, useRef } from 'react';
import { Paper, Stack, Box } from '@mui/material';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { QuestionView } from './QuestionView';
import { Close } from '@mui/icons-material';
import { useDrag, useDrop } from 'react-dnd';
import { DELETE_ITEM, QuestionnaireContext } from '@/context/questionnairePages';

export const Questions = (props) => {
  const { pageIndex, questionIndex, moveCard } = props;
  const { questionnaireState, dispatchQuestionnaireAction } = useContext(QuestionnaireContext);
  const question = useMemo(() => questionnaireState[pageIndex]?.item[questionIndex], [questionnaireState]);

  const handleDeleteQuestion = (pageIndex, questionIndex) => {
    dispatchQuestionnaireAction({ type: DELETE_ITEM, pageIndex, questionIndex });
  };

  const dragRef = useRef(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'card',
    item: { id: question.linkId, pageIndex, questionIndex },
    canDrag: () => !question?.isEditMode,
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'card',
    drop(item, monitor) {
      moveCard(item.pageIndex, item.questionIndex, pageIndex, questionIndex);
    },
    // hover(item, monitor) {
    //   const dragIndex = item.questionIndex;
    //   const hoverIndex = questionIndex;

    //   // Don't replace items with themselves
    //   if (dragIndex === hoverIndex) {
    //     return;
    //   }

    //   // Determine rectangle on screen
    //   const hoverBoundingRect = ref.current.getBoundingClientRect();

    //   // Get vertical middle
    //   const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

    //   // Determine mouse position
    //   const clientOffset = monitor.getClientOffset();

    //   // Get pixels to the top
    //   const hoverClientY = clientOffset.y - hoverBoundingRect.top;

    //   // Only perform the move when the mouse has crossed half of the items height
    //   // When dragging downwards, only move when the cursor is below 50%
    //   // When dragging upwards, only move when the cursor is above 50%

    //   // Dragging downwards
    //   if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
    //     return;
    //   }

    //   // Dragging upwards
    //   if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
    //     return;
    //   }

    //   // Time to actually perform the action
    //   moveCard(item.pageIndex, item.questionIndex, pageIndex, questionIndex);

    //   // Note: we're mutating the monitor item here!
    //   // Generally it's better to avoid mutations,
    //   // but it's good here for the sake of performance
    //   // to avoid expensive index searches.
    //   item.index = hoverIndex;
    // },
  });

  return (
    <>
      <Paper
        ref={drag(drop(dragRef))}
        sx={{ mt: 2, mb: 2, px: 3, py: 2 }}
        style={{ opacity: isDragging ? 1 : 1, border: isDragging ? '1px dashed #ccc' : '1px solid #ccc' }}
      >
        {isDragging ? (
          <></>
        ) : (
          <>
            <Stack direction="row" justifyContent="center">
              <DragIndicatorIcon sx={{ fontSize: '20px', rotate: '90deg', cursor: 'grab' }} />
            </Stack>
            <Box sx={{ textAlign: 'right', marginTop: '-10px' }}>
              <Close
                fontSize="20px"
                sx={{ cursor: 'pointer' }}
                onClick={() => handleDeleteQuestion(pageIndex, questionIndex)}
              />
            </Box>
            <QuestionView pageIndex={pageIndex} questionIndex={questionIndex} question={question} />
          </>
        )}
      </Paper>
    </>
  );
};
