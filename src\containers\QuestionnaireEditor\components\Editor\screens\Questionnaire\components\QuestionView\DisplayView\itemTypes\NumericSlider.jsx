import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TextField, Typography } from '@mui/material';
import { extractExtension } from '@/utility/utils';
import { QuestionText } from '../components/QuestionText';

export const NumericSlider = (props) => {
  const { question, DescriptionComponent } = props;

  const min = extractExtension(question.extension, 'Item/slider-min-value')?.valueDecimal || 0;
  const max = extractExtension(question.extension, 'Item/slider-max-value')?.valueDecimal || 100;
  const minLabel = extractExtension(question.extension, 'Item/slider-min-label')?.valueString || 'Min';
  const maxLabel = extractExtension(question.extension, 'Item/slider-max-label')?.valueString || 'Max';
  const markSize = extractExtension(question.extension, 'Item/slider-steps')?.valueDecimal || 10;
  const step = 1;

  const marks = [];
  let last;
  if (max - min > markSize) {
    for (let i = min; i <= max; i = i + markSize) {
      let mark = { value: i, label: i.toString() };
      marks.push(mark);
      last = i;
    }
    if (last !== max) {
      let mark = { value: max, label: max.toString() };
      marks.push(mark);
    }
  } else {
    marks.push({ value: min, label: min.toString() });
    marks.push({ value: max, label: max.toString() });
  }

  // for (var i = min; i <= max; i = i + step) {
  //   let mark = { value: i, label: i.toString() };
  //   marks.push(mark);
  //   last = i;
  // }
  // if (last !== max) {
  //   let mark = { value: max, label: max.toString() };
  //   marks.push(mark);
  // }

  return (
    <Box>
      <Stack gap={2} sx={{ pointerEvents: 'none' }}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <Box sx={{ pl: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box maxWidth="50%">
              <Typography dangerouslySetInnerHTML={{ __html: minLabel }}></Typography>
            </Box>
            <Box maxWidth="50%">
              <Typography dangerouslySetInnerHTML={{ __html: maxLabel }}></Typography>
            </Box>
          </Box>
          <Slider aria-label="Numeric Slider" value={min} step={step} marks={marks} min={min} max={max} />
        </Box>
        <TextField defaultValue={min} sx={{ maxWidth: '100%' }} />
      </Stack>
    </Box>
  );
};
