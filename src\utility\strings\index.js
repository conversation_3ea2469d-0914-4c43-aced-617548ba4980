export const strings = {
  name: 'Name',
  title: 'Title',
  description: 'Description',
  questionnaireNameTooltip:
    'The questionnaire name will be displayed to organization users only. It will not be displayed to individuals.',
  questionnaireTitleTooltip: 'The questionnaire title will be displayed to individuals.',
  questionnaireDescriptionTooltip:
    'The questionnaire description will be displayed to individuals.  It will appear in the left hand pane below the progress bar.',
  subjectTypeTooltip: 'Select the subject type from the dropdown list',
  edit: 'Edit',
  view: 'View',
  preview: 'Preview',
  duplicate: 'Duplicate',
  delete: 'Delete',
  save: 'Save',
  finalize: 'Finalize',
  pageDescription: 'Page Description',
  page: 'Page',
  properties: 'Properties',
  items: 'Items',
  scoring: 'Scoring',
  reports: 'Reports',
  search: 'Search',
  htmlTemplate: 'HTML Template',
  pdfTemplate: 'PDF Template',
  codeBook: 'Codebook',
  untitledQuestionnaire: 'Untitled Questionnaire',
  reportBuilder: 'Report Builder',
  pdfReportTemplate: 'PDF Report Template',
  htmlReportTemplate: 'HTML Report Template',
  draft: 'DRAFT',
  final: 'FINAL',
  draftBadge: 'Draft',
  finalBadge: 'Final',
  editor: 'Editor',
  preview: 'Preview',
  saveDraft: 'Save Draft',
  publish: 'Publish',
  addPage: 'Add Page',
  addItem: 'Add Item',
  addVariable: 'Add Variable',
  addCondition: 'Add Condition',
  variableName: 'Variable Name',
  selectionRule: 'Selection Rule',
  selectItemType: 'Item Type',
  item: 'Item',
  responseOptions: 'Response Options',
  score: 'Score',
  horizontalOrientation: 'Horizontal Orientation',
  addDescription: 'Add Description',
  addExplanation: 'Add Explanation',
  addDisplayLogic: 'Add Display Logic',
  select: 'Select',
  close: 'Close',
  import: 'Import',
  export: 'Export',
  new: 'New',
  questionnaires: 'Questionnaires',
  required: 'Required',
  report: 'Report',
  selectProperty: 'Property',
  blankQuestionnaire: 'Blank Questionnaire',
  templateGallery: 'Template Gallery',
  itemType: 'Item Type',
  subjectType: 'Subject Type',
  responseValidation: 'Response Validation',
  date: 'Date',
  time: 'Time',
  minValue: 'Min Value',
  maxValue: 'Max Value',
  column: 'Column',
  columnType: 'Column Type',
  addColumn: 'Add Column',
  explanation: 'Explanation',
  mandatoryItems: 'Mandatory Items',
  selectMandatoryItems: 'Please select the items that are mandatory for the individual to respond',
  arithmeticComparison: 'Arithmetic Comparison',
  requiredResponses: 'Required Responses',
  requiredResponsesSubset: 'Required responses count from subset',
  selectMandatoryItems: 'Select mandatory items',
  publishConfirmation: 'Publish Questionnaire',
  canNotEditQuestionnaireOncePublished: 'Cannot edit questionnaire once published',
  selectRepositoryYouWantToPublishIn: 'Select where you want to publish this questionnaire',
  IF: 'IF',
  cancel: 'Cancel',
  ArithmeticToolTip:
    'Arithmetic comparison uses item codes and can be combined with stand mathematical operators to trigger a condition. Example: #{QUS1QGS1} <= 3',
  equation: 'Equation',
  equationToolTip1: 'Equation can use simple numeric value',
  equationToolTip2: 'Example: variable = 2',
  equationToolTip3: 'or a mathematical equation using items or other variables',
  equationToolTip4: 'Example: #{QUS1QGS1}+1+#{QUS2QGS1}',
  equationToolTip5: 'Example: 1.1351 - 0.0389 * #{QUS1QGS1} - 0.0458 * #{QUS1QGS2} + 0.0085 * #{_num45}',
  variableTitle: 'Variables',
  descriptionTooltip:
    'The items below represent a code for each item that can be dragged and dropped to the Arithmetic comparison or Equation text boxes.',
  scoringModalText1: 'Individual must response at least ',
  scoringModalText2: ' of the selected items',
  itemScores: 'Item Scores',
  selectError: 'This field is required',
  condition: 'Condition',
  itemsReq: 'items required',
  displayConditions: 'Display Conditions',
  addDisplayCondition: 'Add Display Condition',
  noSuitableQuestionFound: 'No suitable questions found for display conditions',
  getStartedByAddingItem: 'Get started by adding an item',
  newVariable: 'NewVariable =',
  switchToEditor: 'Switch to Editor',
  switchToPreview: 'Switch to Preview',
  clickToOpenHtmlTemplates: 'Click to open HTML template',
  deleteConfirmation: 'Permanently delete questionnaire',
  private: 'Private',
  public: 'Public',
  finalizeDialogueQuestionTitle: 'Finalize Questionnaire',
  finalizeDialogueQuestionText: 'You cannot edit the questionnaire once it is finalized. This action cannot be undone.',
  ok: 'Ok',
  deleteQuestionnaire: 'Delete Questionnaire',
  deleteFormula: 'Delete Formula',
  thisActionCanNotBeUndone: 'This action cannot be undone',
  clickToOpenCodebookTemplate: 'Click to open Codebook template',
  knowMoreAboutCodebook: 'Know more about Codebook',
  codebookHelpDocument: 'Codebook help document',
  knowMoreAboutHtmlReport: 'Know more about HTML Report',
  reportSyntaxHelpDocument: 'Report syntax help document',
  knowMoreAboutScoring: 'Know more about scoring',
  knowMoreAboutPdfReport: 'Know more about PDF Report',
  knowMoreAboutProperties: 'Know more about questionnaire properties',
  knowMoreAboutItems: 'Know more about questionnaire items',
};

export const placeholders = {
  name: 'Name',
  title: 'Title',
  select: 'Select',
  noValidation: 'No Validation',
  responseOption: 'Response Option',
  otherResponseOption: 'Other Response Option',
  otherResponse: 'Other Response',
  item: 'Item',
  selectItemType: 'Item Type',
  integer: 'Integer',
  number: 'Number',
  minValueLabel: 'Min Value Label',
  maxValueLabel: 'Max Value Label',
  columnLabel: 'Column Label',
  paragraph: 'Paragraph',
  yourAnswer: 'Your Answer',
  yourResponse: 'Your Response',
  from: 'From',
  to: 'To',
  explanation: 'Explanation',
  selectExplanationType: 'Explanation Type',
  description: 'Description',
  selectRule: 'Select Rule',
  selectOperator: 'Operator',
  selectItem: 'Item',
};

export const errors = {
  atLeastOneQuestionInTheQuestionnaire: 'There should be at least one item in the questionnaire',
  firstItemDisplayLogicError: 'First item of the questionnaire cannot have display logic',
  requiredField: 'This field is required',
  lowerRangeSmaller: 'Lower Range must be smaller than the Higher Range',
  higherRangeGreater: 'Higher Range must be greater than the Lower Range',
  lowerAndHigherRangeCanNotBeSame: 'Lower and higher range cannot be the same',
  equationField: 'Equation field cannot be blank for the variable:',
  variableNameError: 'Variable Name must be a combination of alphanumeric characters and underscore',
  fillRequiredFields: 'Please fill in the required fields',
  largeButtonNotAllowed: 'Large Buttons is not allowed in this questionnaire as Radio Buttons is already present',
  radioButtonNotAllowed: 'Radio Buttons is not allowed in this questionnaire as Large Buttons is already present',
  minValueMustBeLess: 'Minimum value must be lower than maximum value',
  maxValueMustBeHigher: 'Maximum value must be greater than minimum value',
  sliderMinValueLimit: 'Min value cannot be less than -100',
  sliderMaxValueLimit: 'Max value cannot be more than 100',
  mustBeAtLeast2CharactersLong: 'Must be at least 2 characters',
};
