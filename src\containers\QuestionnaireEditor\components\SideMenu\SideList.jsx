import React, { useState } from 'react';
import { List, ListItemButton, ListItemText, Paper, ListItemIcon, Collapse } from '@mui/material';
import { ListAlt, Description, ExpandLess, ExpandMore, Equalizer, Book, Settings } from '@mui/icons-material';
import { PagesAndQuestionsListView } from './PagesAndQuestionsListView';
import { strings } from '../../../../utility/strings';
import { editorScreens } from '@/containers/CommonConstants';

export const SideList = (props) => {
  const { handleEditorScreenNavigation } = props;
  const [selectedScreen, setSelectedScreen] = useState(editorScreens.PROPERTIES);
  const [expanded, setExpanded] = useState({
    ITEMS: false,
    REPORTS: false,
  });

  const handleLinkClick = (screen) => {
    if (screen === editorScreens.ITEMS) {
      setExpanded({ ITEMS: true, REPORTS: false });
    } else if (screen === editorScreens.HTML_TEMPLATE || screen === editorScreens.PDF_TEMPLATE) {
      setExpanded({ ITEMS: false, REPORTS: true });
    } else {
      setExpanded({ ITEMS: false, REPORTS: false });
    }
    setSelectedScreen(screen);
    handleEditorScreenNavigation(screen);
  };

  const handleToggle = (event, key) => {
    event.stopPropagation();
    setExpanded((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <>
      <List disablePadding>
        <ListItemButton
          selected={selectedScreen === editorScreens.PROPERTIES}
          onClick={() => handleLinkClick(editorScreens.PROPERTIES)}
        >
          <ListItemIcon>
            <Settings sx={{ fontSize: '20px' }} />
          </ListItemIcon>
          <ListItemText primary={strings.properties} sx={{ ml: -2 }} />
        </ListItemButton>
      </List>
      <List disablePadding>
        <ListItemButton
          selected={selectedScreen === editorScreens.ITEMS}
          onClick={() => handleLinkClick(editorScreens.ITEMS)}
        >
          <ListItemIcon>
            <ListAlt sx={{ fontSize: '20px' }} />
          </ListItemIcon>
          <ListItemText primary={strings.items} sx={{ ml: -2 }} />
          {expanded.ITEMS ? (
            <ExpandLess sx={{ fontSize: '20px' }} onClick={(event) => handleToggle(event, 'ITEMS')} />
          ) : (
            <ExpandMore sx={{ fontSize: '20px' }} onClick={(event) => handleToggle(event, 'ITEMS')} />
          )}
        </ListItemButton>
        <Collapse in={expanded.ITEMS} timeout="auto" unmountOnExit>
          <Collapse in={selectedScreen === editorScreens.ITEMS || expanded.ITEMS} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              <PagesAndQuestionsListView />
            </List>
          </Collapse>
        </Collapse>
      </List>
      <List disablePadding>
        <ListItemButton
          selected={selectedScreen === editorScreens.SCORING}
          onClick={() => handleLinkClick(editorScreens.SCORING)}
        >
          <ListItemIcon>
            <Equalizer sx={{ fontSize: '20px' }} />
          </ListItemIcon>
          <ListItemText primary={strings.scoring} sx={{ ml: -2 }} />
        </ListItemButton>
      </List>
      <List disablePadding>
        <ListItemButton onClick={() => handleLinkClick(editorScreens.HTML_TEMPLATE)}>
          <ListItemIcon>
            <Description sx={{ fontSize: '20px' }} />
          </ListItemIcon>
          <ListItemText primary={strings.reports} sx={{ ml: -2 }} />
          {expanded.REPORTS ? (
            <ExpandLess sx={{ fontSize: '20px' }} onClick={(event) => handleToggle(event, 'REPORTS')} />
          ) : (
            <ExpandMore sx={{ fontSize: '20px' }} onClick={(event) => handleToggle(event, 'REPORTS')} />
          )}
        </ListItemButton>
        <Collapse in={expanded.REPORTS} timeout="auto" unmountOnExit>
          <Collapse
            in={
              selectedScreen === editorScreens.HTML_TEMPLATE ||
              selectedScreen === editorScreens.PDF_TEMPLATE ||
              expanded.REPORTS
            }
            timeout="auto"
            unmountOnExit
          >
            <ListItemButton
              sx={{ pl: 7 }}
              selected={selectedScreen === editorScreens.HTML_TEMPLATE}
              onClick={() => handleLinkClick(editorScreens.HTML_TEMPLATE)}
            >
              <ListItemText primary={strings.htmlTemplate} />
            </ListItemButton>
            <ListItemButton
              sx={{ pl: 7 }}
              selected={selectedScreen === editorScreens.PDF_TEMPLATE}
              onClick={() => handleLinkClick(editorScreens.PDF_TEMPLATE)}
            >
              <ListItemText primary={strings.pdfTemplate} />
            </ListItemButton>
          </Collapse>
        </Collapse>
      </List>
      <List disablePadding>
        <ListItemButton
          selected={selectedScreen === editorScreens.CODE_BOOK}
          onClick={() => handleLinkClick(editorScreens.CODE_BOOK)}
        >
          <ListItemIcon>
            <Book sx={{ fontSize: '20px' }} />
          </ListItemIcon>
          <ListItemText primary={strings.codeBook} sx={{ ml: -2 }} />
        </ListItemButton>
      </List>
    </>
  );
};
