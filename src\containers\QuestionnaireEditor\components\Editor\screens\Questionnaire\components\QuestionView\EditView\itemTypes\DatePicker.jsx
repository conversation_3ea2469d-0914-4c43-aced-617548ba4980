import React, { useState } from 'react';
import { Checkbox, FormControlLabel, FormGroup } from '@mui/material';
import { QuestionTextInput } from './components/QuestionTextInput';
import { characterLength } from '@/containers/CommonConstants';
import { strings } from '@/utility/strings';

export const DatePicker = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;

  const [questionText, setQuestionText] = useState(question?.text || '');
  const [checkedValues, setCheckedValues] = useState({
    date: true,
    dateTime: question?.type === 'dateTime' || false,
  });

  const handleQuestionCreation = (question) => {
    handleCreateQuestion(pageIndex, questionIndex, { ...question });
  };

  const handleQuestionTextChange = (event) => {
    const newText = event.target.value.slice(0, characterLength.itemTextLength);
    setQuestionText(newText);
    let newQuestion = {
      ...question,
      text: event.target.value,
      type: checkedValues.date && checkedValues.dateTime ? 'dateTime' : 'date',
    };
    handleQuestionCreation(newQuestion);
  };

  const handleOnChange = (event) => {
    setCheckedValues(() => {
      const newState = {
        ...checkedValues,
        [event.target.name]: event.target.checked,
      };

      let newQuestion = {
        ...question,
        type: newState.date && newState.dateTime ? 'dateTime' : 'date',
      };

      handleQuestionCreation(newQuestion);
      return newState;
    });
  };

  return (
    <>
      <QuestionTextInput
        value={questionText}
        onChange={handleQuestionTextChange}
        question={question}
        characterLimit={characterLength.itemTextLength}
      />
      {DescriptionComponent}
      <FormGroup sx={{ mt: 2 }} onChange={handleOnChange}>
        <FormControlLabel
          control={<Checkbox defaultChecked checked={checkedValues.date} name="date" disabled sx={{ color: '#999' }} />}
          label={strings.date}
        />
        <FormControlLabel
          control={<Checkbox checked={checkedValues.dateTime} name="dateTime" sx={{ color: '#999' }} />}
          label={strings.time}
        />
      </FormGroup>
    </>
  );
};
