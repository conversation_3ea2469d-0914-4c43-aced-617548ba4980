import React, { useContext, useMemo, useState, useEffect } from 'react';
import {
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
  FormLabel,
} from '@mui/material';
import { Close } from '@mui/icons-material';
import { extractExtension } from '@/utility/utils';
import { characterLength } from '@/containers/CommonConstants';
import { placeholders, strings } from '@/utility/strings';
import { ValidationErrorContext } from '@/context';

const explanationOptions = [
  {
    value: 'INFO',
    display: 'Information',
  },
  {
    value: 'WARNING',
    display: 'Warning',
  },
  {
    value: 'ERROR',
    display: 'Error',
  },
];

const EXPLANATION_FLAG_EXTENSION_URL = 'Item/explanation-flag';
const EXPLANATION_EXTENSION_URL = 'Item/explanation';

export const Explanation = (props) => {
  const { question, handleExplanation, explanationExtension, handleDisableExplanation } = props;
  const { questionErrors } = useContext(ValidationErrorContext);
  const error = useMemo(() => questionErrors[question.linkId], [questionErrors]);

  const explanationType = useMemo(() => {
    return extractExtension(explanationExtension, EXPLANATION_FLAG_EXTENSION_URL)?.valueString;
  }, [explanationExtension]);

  const [explanation, setExplanation] = useState(
    explanationOptions.find((option) => option.value === question?.explanation?.type) || { value: '', display: '' },
  );
  const [explanationText, setExplanationText] = useState(question?.explanation?.text || '');

  useEffect(() => {
    const explanationExtensionText = question.extension.find((obj) => obj.url === 'Item/explanation');
    const explanationExtensionFlag = question.extension.find((obj) => obj.url === 'Item/explanation-flag');
    if (explanationExtensionText) {
      explanationExtensionText.valueString = explanationText;
    }
    if (explanationExtensionFlag) {
      explanationExtensionFlag.valueString = question?.explanation?.type;
    }
  }, [question]);

  const handleDropdown = (event) => {
    setExplanation(event.target.value);
    const explanationExtension = [
      {
        url: EXPLANATION_EXTENSION_URL,
        valueString: explanationText || null,
      },
      {
        url: EXPLANATION_FLAG_EXTENSION_URL,
        valueString: event.target.value.value || null,
      },
    ];

    handleExplanation(explanationExtension, {
      text: explanationText,
      type: event.target.value.value,
    });
  };

  const handleOnChange = (event) => {
    // setExplanationText(event.target.value);
    // const explanationExtension = [
    //   {
    //     url: EXPLANATION_EXTENSION_URL,
    //     valueString: event.target.value || null,
    //   },
    //   {
    //     url: EXPLANATION_FLAG_EXTENSION_URL,
    //     valueString: explanation?.value || null,
    //   },
    // ];

    // handleExplanation(explanationExtension, {
    //   text: event.target.value,
    //   type: explanation?.value,
    // });
    const newText = event.target.value.slice(0, characterLength.descriptionAndExplanationLength);
    setExplanationText(newText);
    const explanationExtension = [
      {
        url: EXPLANATION_EXTENSION_URL,
        valueString: newText || null,
      },
      {
        url: EXPLANATION_FLAG_EXTENSION_URL,
        valueString: explanation?.value || null,
      },
    ];

    handleExplanation(explanationExtension, {
      text: newText,
      type: explanation?.value,
    });
  };
  const isLimitReached = explanationText?.length >= characterLength.descriptionAndExplanationLength;

  const handleRemoveExplanation = () => {
    setExplanation({ value: '', display: '' });
    handleDisableExplanation();
  };

  return (
    <>
      <Typography mb={1}>
        <FormLabel required={true} sx={{ color: '#000' }}>
          {strings.explanation}
        </FormLabel>
      </Typography>
      <Stack direction="row" alignItems="flex-start" justifyContent="space-between">
        <Grid container spacing={2}>
          <Grid item sm={8}>
            <FormControl fullWidth>
              <TextField
                value={explanationText}
                onChange={handleOnChange}
                placeholder={placeholders.explanation}
                error={error && !!error['explanation.text']}
                helperText={error && error['explanation.text']}
                inputProps={{ maxLength: characterLength.descriptionAndExplanationLength }}
              />
              {isLimitReached && (
                <FormHelperText
                  sx={{ textAlign: 'right', color: '#bdc1cc' }}
                >{`${characterLength.descriptionAndExplanationLength} / ${characterLength.descriptionAndExplanationLength}`}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item sm={4}>
            <Select
              fullWidth
              size="small"
              value={explanation?.display}
              onChange={handleDropdown}
              displayEmpty
              renderValue={(selected) => selected || placeholders.selectExplanationType}
              error={error && !!error['explanation.type']}
            >
              {explanationOptions.map((explanation) => (
                <MenuItem key={explanation.value} value={explanation}>
                  {explanation.display}
                </MenuItem>
              ))}
            </Select>
            {error && !!error['explanation.type'] && <FormHelperText error>{error['explanation.type']}</FormHelperText>}
          </Grid>
        </Grid>
        <IconButton onClick={handleRemoveExplanation} sx={{ pr: 0 }}>
          <Close sx={{ fontSize: '20px' }} />
        </IconButton>
      </Stack>
    </>
  );
};
