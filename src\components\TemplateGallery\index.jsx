import React from 'react';
import { Box, Typography, Modal, Paper, Stack, Grid, Divider } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { strings } from '../../utility/strings';
import newTemplateImage from '@/assets/new-template.jpg';
import Image from 'next/image';
import { pages } from '@/containers/CommonConstants';

export const TemplateGallery = (props) => {
  const { open, setOpen, handleAddNew } = props;

  const templates = [
    {
      id: 0,
      imgPath: newTemplateImage,
      imgAlt: strings.blankQuestionnaire,
      label: strings.blankQuestionnaire,
      onClick: () => handleAddNew(pages.questionnaireEditor),
    },
  ];

  const modalStyle = {
    position: 'absolute',
    top: '10%',
    left: '50%',
    transform: 'translate(-50%, -10%)',
    width: 600,
    bgcolor: 'background.paper',
    boxShadow: 24,
    borderRadius: '4px',
  };

  const handleClose = () => setOpen(false);

  return (
    <div>
      <Modal open={open} onClose={handleClose}>
        <Box sx={modalStyle}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ my: 1, px: 2, py: 1 }}>
            <Typography variant="h3">{strings.templateGallery}</Typography>
            <CloseIcon onClick={handleClose} sx={{ fontSize: '20px', cursor: 'pointer' }} />
          </Stack>
          <Divider />
          <Grid container sx={{ p: 3 }}>
            <Grid item sm={4}>
              {templates.map((template) => (
                <Paper
                  key={template.id}
                  sx={{ width: 150, cursor: 'pointer', mb: 2, height: 200 }}
                  onClick={() => {
                    template.onClick();
                    handleClose();
                  }}
                >
                  <Stack>
                    <Box>
                      <Image src={template.imgPath} alt={template.imgAlt} width={149} height={110} />
                    </Box>
                    <Box sx={{ p: 1, padding: '9px' }}>
                      <Typography>{template.label}</Typography>
                    </Box>
                    <Box></Box>
                  </Stack>
                </Paper>
              ))}
            </Grid>
          </Grid>
        </Box>
      </Modal>
    </div>
  );
};
