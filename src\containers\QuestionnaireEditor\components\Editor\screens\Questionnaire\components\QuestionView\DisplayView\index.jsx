import React, { useContext } from 'react';
import { Box } from '@mui/material';
import {
  Display,
  Text,
  Paragraph,
  Checkboxes,
  RadioQuestion,
  DatePicker,
  LargeButton,
  NumericSlider,
  LinearScale,
  BodyDiagram,
  DataGrid,
} from './itemTypes';
import {
  extractExtension,
  isBodyDiagramQuestion,
  isCheckboxQuestion,
  isDropdownQuestion,
  isIntegerOnlyQuestion,
  isLargeButtonQuestion,
  isLinearScaleQuestion,
  isNumericSliderQuestion,
  isRadioButtonQuestion,
} from '@/utility/utils';
import { Explanation } from './components/Explanation';
import { Description } from './components/Description';
import { OPEN_EDIT_MODE, QuestionnaireContext } from '@/context/questionnairePages';

export const DisplayView = (props) => {
  const { question, pageIndex, questionIndex } = props;
  const { dispatchQuestionnaireAction } = useContext(QuestionnaireContext);

  const handleOpenEditMode = () => {
    dispatchQuestionnaireAction({
      type: OPEN_EDIT_MODE,
      pageIndex,
      questionIndex,
    });
  };

  const getPage = () => {
    let Page;
    if (isLinearScaleQuestion(question)) {
      Page = LinearScale;
    } else if (isNumericSliderQuestion(question)) {
      Page = NumericSlider;
    } else if (isBodyDiagramQuestion(question)) {
      Page = BodyDiagram;
    } else if (isDropdownQuestion(question)) {
      Page = RadioQuestion;
    } else if (isLargeButtonQuestion(question)) {
      Page = LargeButton;
    } else if (isRadioButtonQuestion(question)) {
      Page = RadioQuestion;
    } else if (isCheckboxQuestion(question)) {
      Page = Checkboxes;
    } else if (question.type === 'text') {
      const TEXT_QUESTION_ALLOWED_LENGTH = 255;
      const maxAllowedLengthExtension = extractExtension(question.extension, 'Item/max-length');

      Page = maxAllowedLengthExtension?.valueInteger > TEXT_QUESTION_ALLOWED_LENGTH ? Paragraph : Text;
    } else if ((question.type === 'integer' && isIntegerOnlyQuestion(question)) || question.type === 'decimal') {
      Page = Text;
    } else if (question.type.includes('date')) {
      Page = DatePicker;
    } else if (question.type.includes('group')) {
      Page = DataGrid;
    } else if (question.type === 'display') {
      Page = Display;
    } else {
      return <></>;
    }

    return <Page question={question} DescriptionComponent={<Description question={question} />} />;
  };

  return (
    <>
      <Box sx={{ cursor: 'pointer' }} onClick={handleOpenEditMode}>
        {getPage()}
        <Box sx={{ mt: 3, mb: 2 }}>
          <Explanation question={question} />
        </Box>
      </Box>
    </>
  );
};
