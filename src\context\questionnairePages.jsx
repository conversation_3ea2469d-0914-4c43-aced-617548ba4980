import React, { createContext, useEffect, useReducer, useState } from 'react';
import { addKeysToQuestionnaireItem } from '@/utility/utils';
import { generateShortUuid } from '@/utility/utils';

export const ADD_PAGE = 'ADD_PAGE';
export const DELETE_PAGE = 'DELETE_PAGE';
export const DUPLICATE_PAGE = 'DUPLICATE_PAGE';
export const UPDATE_PAGES = 'UPDATE_PAGES';
export const ADD_ITEM = 'ADD_ITEM';
export const DELETE_ITEM = 'DELETE_ITEM';
export const DUPLICATE_ITEM = 'DUPLICATE_ITEM';
export const UPDATE_ITEM = 'UPDATE_ITEM';
export const OPEN_EDIT_MODE = 'OPEN_EDIT_MODE';
export const CLOSE_EDIT_MODE = 'CLOSE_EDIT_MODE';
export const REINITIALIZE_QUESTIONNAIRE = 'REINITIALIZE_QUESTIONNAIRE';

// Define the initial state of your context
const questionnaireStateStructure = [
  {
    linkId: `Group1`,
    item: [
      {
        id: generateShortUuid(),
        isEditMode: true,
        linkId: 'Item1',
        type: '',
        text: '',
      },
    ],
    id: `group-${generateShortUuid()}`,
    type: 'group',
  },
];

const handleAddNewPage = (newPageId, newItemId) => {
  return {
    linkId: newPageId,
    item: [
      {
        id: generateShortUuid(),
        isEditMode: true,
        linkId: newItemId,
        type: '',
        text: '',
        answerOption: [],
      },
    ],
    id: `group-${generateShortUuid()}`,
    type: 'group',
  };
};

const handleDeletePage = (questionnairePages, pageIndex) => {
  let newPages = structuredClone(questionnairePages);
  newPages.splice(pageIndex, 1);

  return newPages;
};

const handleDuplicatePage = (questionnairePages, pageIndex, newPageId, newItemId, updateLastQuestionLinkId) => {
  let newPages = structuredClone(questionnairePages);
  var itemId = parseInt(newItemId.replace('Item', ''), 10);
  let duplicatedPage = {
    linkId: newPageId,
    item: newPages[pageIndex]?.item?.map((question) => {
      const newLinkId = `Item${itemId}`;
      itemId++;

      return {
        ...question,
        id: question?.type === 'group' ? `complex-${generateShortUuid()}` : generateShortUuid(),
        linkId: newLinkId,
      };
    }),
    id: `group-${generateShortUuid()}`,
    type: 'group',
  };
  updateLastQuestionLinkId(itemId - 1);

  newPages.splice(pageIndex + 1, 0, duplicatedPage);

  return newPages;
};

const handleOpenEditMode = (questionnairePages, pageIndex, questionIndex) => {
  let newPages = structuredClone(questionnairePages);
  let questions = structuredClone(newPages[pageIndex].item);

  questions[questionIndex].isEditMode = true;
  newPages[pageIndex].item = questions;

  return newPages;
};

const handleCloseEditMode = (questionnairePages, pageIndex, questionIndex) => {
  let newPages = structuredClone(questionnairePages);
  let questions = structuredClone(newPages[pageIndex].item);

  questions[questionIndex].isEditMode = false;
  newPages[pageIndex].item = questions;

  return newPages;
};

const handleAddQuestion = (questionnairePages, pageIndex, newItemId) => {
  let pages = structuredClone(questionnairePages);
  let questions = [...pages[pageIndex].item];

  questions = [
    ...questions,
    {
      id: generateShortUuid(),
      isEditMode: true,
      linkId: newItemId,
      type: '',
      text: '',
    },
  ];

  pages[pageIndex].item = questions;

  return pages;
};

const handleDuplicateQuestion = (questionnairePages, pageIndex, questionIndex, newItemId, updateLastQuestionLinkId) => {
  let newPages = structuredClone(questionnairePages);
  let questions = [...newPages[pageIndex].item];
  const originalQuestion = questions[questionIndex];
  const newId = generateShortUuid();

  let duplicatedQuestion = {
    ...originalQuestion,
    linkId: newItemId,
    id: originalQuestion.type === 'group' ? `complex-${newId}` : newId,
  };

  questions.splice(questionIndex + 1, 0, duplicatedQuestion);

  newPages[pageIndex].item = questions;
  updateLastQuestionLinkId(parseInt(newItemId.replace('Item', ''), 10) + 1);

  return newPages;
};

const handleDeleteQuestion = (questionnairePages, pageIndex, questionIndex) => {
  let newPages = structuredClone(questionnairePages);
  let questions = [...newPages[pageIndex].item];
  questions.splice(questionIndex, 1);

  newPages[pageIndex].item = questions;

  return newPages;
};

const handleUpdateQuestion = (questionnairePages, pageIndex, questionIndex, updatedQuestion) => {
  let newPages = structuredClone(questionnairePages);
  let questions = structuredClone(newPages[pageIndex].item);
  let question = { ...updatedQuestion };

  questions[questionIndex] = question;
  newPages[pageIndex].item = questions;

  return newPages;
};

// Define a reducer function
const reducer = (state, action) => {
  const {
    type,
    pageIndex,
    questionIndex,
    updatedQuestion,
    updatedPages,
    nextPageLinkId,
    nextItemLinkId,
    updateLastQuestionLinkId,
  } = action || {};

  switch (type) {
    case ADD_PAGE:
      const newPage = handleAddNewPage(nextPageLinkId, nextItemLinkId);
      return [...state, newPage];

    case DELETE_PAGE:
      const pagesAfterDelete = handleDeletePage(state, pageIndex);
      return pagesAfterDelete;

    case DUPLICATE_PAGE:
      const pagesAfterDuplicate = handleDuplicatePage(
        state,
        pageIndex,
        nextPageLinkId,
        nextItemLinkId,
        updateLastQuestionLinkId,
      );
      return pagesAfterDuplicate;

    case UPDATE_PAGES:
      return updatedPages;

    case ADD_ITEM:
      const newItems = handleAddQuestion(state, pageIndex, nextItemLinkId);
      return newItems;

    case DUPLICATE_ITEM:
      const itemsAfterDuplicate = handleDuplicateQuestion(
        state,
        pageIndex,
        questionIndex,
        nextItemLinkId,
        updateLastQuestionLinkId,
      );
      return itemsAfterDuplicate;

    case DELETE_ITEM:
      const itemsAfterDelete = handleDeleteQuestion(state, pageIndex, questionIndex);
      return itemsAfterDelete;

    case UPDATE_ITEM:
      const updatedItem = handleUpdateQuestion(state, pageIndex, questionIndex, updatedQuestion);
      return updatedItem;

    case OPEN_EDIT_MODE:
      return handleOpenEditMode(state, pageIndex, questionIndex);

    case CLOSE_EDIT_MODE:
      return handleCloseEditMode(state, pageIndex, questionIndex);

    case REINITIALIZE_QUESTIONNAIRE:
      return updatedPages;

    default:
      return state;
  }
};

// Create the context
export const QuestionnaireContext = createContext();

// Create a provider component
export const QuestionnaireProvider = ({ children, existingQuestionnaireData }) => {
  const parsedQuestionnaireData = addKeysToQuestionnaireItem(structuredClone(existingQuestionnaireData?.item));

  const [initialState, setInitialState] = useState(parsedQuestionnaireData || questionnaireStateStructure);
  const [state, dispatch] = useReducer(reducer, initialState);

  useEffect(() => {
    if (existingQuestionnaireData) {
      const parsedQuestionnaireData = addKeysToQuestionnaireItem(structuredClone(existingQuestionnaireData?.item));
      const updatedState = parsedQuestionnaireData; // TODO: keys not getting added
      setInitialState(updatedState);
    }
  }, [existingQuestionnaireData?.item]);

  return (
    <QuestionnaireContext.Provider
      value={{ questionnaireState: state, dispatchQuestionnaireAction: dispatch, initialState }}
    >
      {children}
    </QuestionnaireContext.Provider>
  );
};
