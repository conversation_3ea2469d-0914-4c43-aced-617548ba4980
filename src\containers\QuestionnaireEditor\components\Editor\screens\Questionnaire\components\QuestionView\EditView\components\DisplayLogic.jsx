import React, { useContext, useMemo, useState } from 'react';
import {
  Box,
  Button,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
  IconButton,
  FormLabel,
} from '@mui/material';
import { Add, Close } from '@mui/icons-material';
import { placeholders, strings } from '@/utility/strings';
import { QuestionnaireContext, ValidationErrorContext } from '@/context';

const operators = [
  {
    symbol: '=',
    label: 'Equal',
  },
  {
    symbol: '!=',
    label: 'Not equal',
  },
  {
    symbol: '>',
    label: 'Greater than',
  },
  {
    symbol: '<',
    label: 'Less than',
  },
  {
    symbol: '>=',
    label: 'Greater than or equal to',
  },
  {
    symbol: '<=',
    label: 'Less than or equal to',
  },
];

// const extractDisplayConditionsQuestions = (data, currentPageIndex, currentQuestionIndex) => {
//   const result = data
//     .slice(0, currentPageIndex + 1)
//     .flatMap((page, pageIndex) =>
//       (console.log('page.item', page.item) || page.item || [])
//         .filter(
//           (question, questionIndex) =>
//             pageIndex <= currentPageIndex &&
//             questionIndex < currentQuestionIndex &&
//             ['integer', 'decimal', 'choice'].includes(question.type),
//         )
//         .map(({ linkId, text }) => ({ linkId, questionText: text })),
//     );

//   return result;
// };

function extractDisplayConditionsQuestions(data, currentPageIndex, currentQuestionIndex) {
  let filteredElements = [];

  for (let i = 0; i < data.length; i++) {
    const group = data[i];

    if (group.item) {
      for (let j = 0; j < group.item.length; j++) {
        const item = group.item[j];

        if (item.type && ['integer', 'choice', 'decimal'].includes(item.type)) {
          if (i < currentPageIndex || (i === currentPageIndex && j < currentQuestionIndex)) {
            filteredElements.push({
              linkId: item.linkId,
              questionText: item.text,
            });
          }
        }
      }
    }
  }

  return filteredElements;
}

export const DisplayLogic = (props) => {
  const { question, pageIndex, questionIndex, handleDisplayCondition, handleDisableDisplayLogic } = props;
  const { questionnaireState } = useContext(QuestionnaireContext);
  const { questionErrors } = useContext(ValidationErrorContext);

  const isFirstItem = pageIndex === 0 && questionIndex === 0;

  useMemo(() => {
    if (isFirstItem && question?.enableWhen) {
      handleDisableDisplayLogic();
    }
  }, [isFirstItem, question, handleDisableDisplayLogic]);

  if (isFirstItem) return null;

  const items = useMemo(
    () => extractDisplayConditionsQuestions(questionnaireState, pageIndex, questionIndex),
    [questionnaireState],
  );

  const operatorsMap = new Map(operators.map((operator) => [operator.symbol, operator]));
  const itemsMap = new Map(items.map((item) => [item.linkId, item]));

  const error = useMemo(() => questionErrors[question.linkId], [questionErrors]);

  const [conditions, setConditions] = useState(
    question?.enableWhen
      ? question?.enableWhen
      : [
          {
            question: '',
            operator: '',
            answerString: undefined,
          },
        ],
  );

  const handleItemDropdown = (event, index) => {
    setConditions((prevCondition) => {
      const newCondition = [...prevCondition];
      newCondition[index].question = event.target.value.linkId;

      handleDisplayCondition(newCondition);
      return newCondition;
    });
  };

  const handleOperatorDropdown = (event, index) => {
    setConditions((prevCondition) => {
      const newCondition = [...prevCondition];
      newCondition[index].operator = event.target.value.symbol;

      handleDisplayCondition(newCondition);
      return newCondition;
    });
  };

  const handleScoreChange = (event, index) => {
    const inputText = event.target.value;
    const numberCheckRegEx = /^\d*$/;
    if (numberCheckRegEx.test(inputText)) {
      setConditions((prevCondition) => {
        const newCondition = [...prevCondition];
        newCondition[index].answerString = inputText;

        handleDisplayCondition(newCondition);
        return newCondition;
      });
    }
  };

  const handleAddDisplayCondition = () => {
    setConditions((prevCondition) => {
      const newCondition = [...prevCondition];
      newCondition.push({
        question: '',
        operator: '',
        answerString: undefined,
      });

      return newCondition;
    });
  };

  const handleDeleteDisplayCondition = (index) => {
    if (conditions.length === 1) {
      setConditions([
        {
          question: '',
          operator: '',
          answerString: '',
        },
      ]);
      handleDisableDisplayLogic();
      return;
    }
    setConditions((prevCondition) => {
      const newCondition = [...prevCondition];
      newCondition.splice(index, 1);

      handleDisplayCondition(newCondition);
      return newCondition;
    });
  };

  return (
    <>
      <Typography variant="bodyTitle" sx={{ mt: 2, mb: 2 }}>
        {strings.displayConditions}
      </Typography>

      {conditions.map((condition, conditionIndex) => (
        <Box key={conditionIndex}>
          <Stack direction="row" gap={2} alignItems="flex-start" justifyContent="space-between">
            <Grid container spacing={2}>
              <Grid item xs={12} sm={7} lg={7.5} mt={1} mb={1}>
                <Typography variant="bodyTitle">
                  <FormLabel required={true} sx={{ color: '#000' }}>
                    {strings.item}
                  </FormLabel>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={5} lg={4.5}>
                <Typography variant="bodyTitle">{strings.answerString}</Typography>
              </Grid>
            </Grid>
          </Stack>
          <Stack direction="row" gap={2} alignItems="flex-start" justifyContent="space-between">
            <Grid container spacing={2}>
              <Grid item xs={12} sm={7} lg={7.5}>
                <Select
                  fullWidth
                  size="small"
                  value={itemsMap.get(condition?.question) || ''}
                  displayEmpty
                  renderValue={(selected) => selected?.questionText || placeholders.selectItem}
                  onChange={(event) => handleItemDropdown(event, conditionIndex)}
                  error={error && !!error[`enableWhen[${conditionIndex}].question`]}
                >
                  {items.length ? (
                    items.map((item) => (
                      <MenuItem key={item.linkId} value={item}>
                        {item.questionText}
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem disabled key={strings.noSuitableQuestionFound} value={strings.noSuitableQuestionFound}>
                      {strings.noSuitableQuestionFound}
                    </MenuItem>
                  )}
                </Select>
                {error && !!error[`enableWhen[${conditionIndex}].question`] && (
                  <FormHelperText error>{error[`enableWhen[${conditionIndex}].question`]}</FormHelperText>
                )}
              </Grid>
              <Grid item xs={12} sm={5} lg={4.5}>
                <Grid container spacing={2}>
                  <Grid item xs={8} sm={8}>
                    <Select
                      fullWidth
                      size="small"
                      value={operatorsMap.get(condition?.operator) || ''}
                      displayEmpty
                      renderValue={(selected) => selected?.label || placeholders.selectOperator}
                      onChange={(event) => handleOperatorDropdown(event, conditionIndex)}
                      error={error && !!error[`enableWhen[${conditionIndex}].operator`]}
                    >
                      {operators?.map((operator, index) => (
                        <MenuItem key={index} value={operator}>
                          {operator?.label}
                        </MenuItem>
                      ))}
                    </Select>
                    {error && !!error[`enableWhen[${conditionIndex}].operator`] && (
                      <FormHelperText error>{error[`enableWhen[${conditionIndex}].operator`]}</FormHelperText>
                    )}
                  </Grid>
                  <Grid item xs={4} sm={4}>
                    <TextField
                      value={condition?.answerString || ''}
                      onChange={(event) => handleScoreChange(event, conditionIndex)}
                      error={error && !!error[`enableWhen[${conditionIndex}].answerString`]}
                      helperText={error && error[`enableWhen[${conditionIndex}].answerString`]}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
            <IconButton onClick={() => handleDeleteDisplayCondition(conditionIndex)} sx={{ pr: 0 }}>
              <Close sx={{ fontSize: '20px', color: '#777' }} />
            </IconButton>
          </Stack>
          {conditions.length > 1 && conditionIndex !== conditions.length - 1 && (
            <Stack alignItems="center" sx={{ mt: 2 }}>
              <Typography fontWeight="bold">And</Typography>
            </Stack>
          )}
        </Box>
      ))}

      <Stack direction="row" justifyContent="flex-start" mb={1}>
        <Button
          variant="text"
          startIcon={<Add />}
          onClick={handleAddDisplayCondition}
          sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
        >
          {strings.addDisplayCondition}
        </Button>
      </Stack>
    </>
  );
};
