import React, { useContext } from 'react';
import { QuestionnaireDetail } from './components/QuestionnaireDetail';
import { QuestionnairePage } from './components/QuestionnairePage';
import { QuestionnaireDetailsContext } from '@/context';

export const QuestionnaireScreen = (props) => {
  const { getQuestionnaireDetails } = props;
  const { questionnaireDetails } = useContext(QuestionnaireDetailsContext);

  return (
    <div style={{ pointerEvents: questionnaireDetails.status === 'final' ? 'none' : 'auto' }}>
      {/* <QuestionnaireDetail getQuestionnaireDetails={getQuestionnaireDetails} /> */}
      <QuestionnairePage />
    </div>
  );
};
