import React from 'react';
import { useContext, useMemo } from 'react';
import { List, ListItem, ListItemButton, ListItemText, Paper, Typography, Box } from '@mui/material';
import { strings } from '@/utility/strings';
import { removeHtmlTags } from '@/utility/utils';
import { QuestionnaireContext } from '@/context';

export const PagesAndQuestionsListView = () => {
  const { questionnaireState } = useContext(QuestionnaireContext);
  const noItemPresent = useMemo(() => {
    let isNoItemPresent = true;
    for (const page of questionnaireState) {
      if (page?.item?.length >= 2) {
        isNoItemPresent = page.item.every((item) => !item.text);
      } else if (page?.item?.length === 1) {
        isNoItemPresent = !page.item[0]?.text;
      }
      if (!isNoItemPresent) {
        break;
      }
    }

    return isNoItemPresent;
  }, [questionnaireState]);

  return (
    <Paper sx={{ height: 'auto', overflowY: 'auto', maxHeight: '380px', border: 'none' }}>
      {!noItemPresent ? (
        <List>
          {questionnaireState.map((page, pageIndex) => (
            <div key={pageIndex}>
              <ListItem disablePadding>
                <ListItemButton sx={{ pl: 5 }}>
                  <Typography sx={{ pl: 2, lineHeight: 0.7 }}>
                    {strings.page} {pageIndex + 1}
                  </Typography>
                </ListItemButton>
              </ListItem>
              <List component="div" disablePadding sx={{ pl: 7 }}>
                {page?.item?.length ? (
                  page?.item?.map(
                    (question, questionIndex) =>
                      !question.isEditMode && (
                        <ListItem key={questionIndex} disablePadding>
                          <ListItemButton sx={{ py: 0.1, minHeight: 'unset' }}>
                            <ListItemText
                              primary={removeHtmlTags(question.text)}
                              primaryTypographyProps={{
                                style: { overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' },
                              }}
                            />
                          </ListItemButton>
                        </ListItem>
                      ),
                  )
                ) : (
                  <Typography>No Item</Typography>
                )}
              </List>
            </div>
          ))}
        </List>
      ) : (
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            flexGrow: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <List sx={{ mt: 2 }}>{strings.getStartedByAddingItem}</List>
        </Box>
      )}
    </Paper>
  );
};
