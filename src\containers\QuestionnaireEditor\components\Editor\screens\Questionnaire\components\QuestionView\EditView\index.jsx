'use client';
import React, { useContext, useMemo, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ack,
  FormControlLabel,
  FormControl,
  Switch,
  MenuItem,
  Divider,
  Select,
  Typography,
  Box,
  Checkbox,
  FormLabel,
} from '@mui/material';
import { MenuList } from '@/components';
import {
  Checkboxes,
  RadioButton,
  Dropdown,
  LargeButton,
  Display,
  Paragraph,
  Text,
  Integer,
  Number,
  DatePicker,
  BodyDiagram,
  DataGrid,
  NumericSlider,
  LinearScale,
} from './itemTypes';
import { FileCopy, MoreVert, Add, AccountTree } from '@mui/icons-material';
import { questionTemplates, questionTypeOptions } from '@/containers/CommonConstants';
import { generateShortUuid } from '@/utility/utils';
import { strings, placeholders, errors as errorMessages } from '@/utility/strings';
import {
  extractExtension,
  isBodyDiagramQuestion,
  isCheckboxQuestion,
  isDropdownQuestion,
  isIntegerOnlyQuestion,
  isLarge<PERSON>uttonQuestion,
  isLinearScaleQuestion,
  isNumericSliderQuestion,
  isRadioButtonQuestion,
} from '@/utility/utils';
import { Explanation, Description, DisplayLogic } from './components';
import useSnackbar from '@/hooks/useNotification';
import { DUPLICATE_ITEM, QuestionnaireContext, CLOSE_EDIT_MODE, UPDATE_ITEM } from '@/context/questionnairePages';
import { useGenerateLinkId } from '@/hooks/useGenerateLinkId';
import { useDebouncing } from '@/hooks/useDebouncing';
import { validateQuestion } from '@/utility/validations';
import { ValidationErrorContext } from '@/context';

const createMenuItems = (questionArrays) => {
  let items = [];

  questionArrays.forEach((questions, i) => {
    for (const question of questions) {
      items.push(
        <MenuItem key={question.type} value={question}>
          {question.label}
        </MenuItem>,
      );
    }

    if (i < questionArrays.length - 1) {
      items.push(<Divider key={i} />);
    }
  });

  return items;
};

export const EditView = (props) => {
  const { pageIndex, questionIndex, selectedQuestionType, setSelectedQuestionType } = props;
  const openSnackbar = useSnackbar();
  const { generateItemLinkId, updateLastQuestionLinkId } = useGenerateLinkId();
  const { debounceFunction } = useDebouncing();
  const { questionnaireState, dispatchQuestionnaireAction } = useContext(QuestionnaireContext);
  const { questionErrors, handleQuestionValidationErrors } = useContext(ValidationErrorContext);

  const [anchorEl, setAnchorEl] = useState(null);

  const question = useMemo(() => questionnaireState[pageIndex]?.item[questionIndex], [questionnaireState]);
  const explanationExtension = useMemo(
    () => extractExtension(question.extension, 'Item/explanation')?.valueString || null,
    [question.extension],
  );

  const descriptionExtension = useMemo(() => {
    return extractExtension(question.extension, 'Item/description')?.valueString || null;
  }, [question.extension]);

  const showInReportExtension = useMemo(() => {
    return extractExtension(question.extension, 'Item/hide-question')?.valueBoolean ?? true;
  }, [question.extension]);

  const [isExplanationEnabled, setIsExplanationEnabled] = useState(question?.explanation ? true : false);
  const [isDescriptionEnabled, setIsDescriptionEnabled] = useState(question?.description ? true : false);
  const [showInReportEnabled, setShowInReportEnabled] = useState(
    question?.extension?.length ? !Boolean(showInReportExtension) : true,
  );
  const [isRequiredEnabled, setIsRequiredEnabled] = useState(question?.required);
  const [isDisplayLogicEnabled, setIsDisplayLogicEnabled] = useState(!!question?.enableWhen?.length || false);

  let largeButtonsCount = useMemo(() => {
    let count = 0;
    for (const questionnaire of questionnaireState) {
      for (const item of questionnaire.item) {
        const foundLargeButtons = item.extension?.some(
          (ext) => ext.url === 'Item/display-large-buttons' && ext.valueBoolean === true,
        );
        if (foundLargeButtons) {
          count++;
        }
      }
    }
    return count;
  }, [questionnaireState]);

  let radioButtonsCount = useMemo(() => {
    let count = 0;
    for (const questionnaire of questionnaireState) {
      for (const item of questionnaire.item) {
        const foundRadioButtons =
          item.extension?.some((ext) => ext.url === 'Item/question-type-id' && ext.valueInteger === 5515) &&
          !item.extension?.some((ext) => ext.url === 'Item/display-large-buttons' && ext.valueBoolean === true) &&
          !item.extension?.some((ext) => ext.url === 'Item/display-type' && ext.valueString === 'choice-bar');
        if (foundRadioButtons) {
          count++;
        }
      }
    }
    return count;
  }, [questionnaireState]);

  const handleEnableDisplayLogic = () => {
    if (pageIndex === 0 && questionIndex === 0) {
      openSnackbar({ variant: 'error', msg: errorMessages.firstItemDisplayLogicError });
      return;
    }
    setIsDisplayLogicEnabled(true);
    dispatchQuestionnaireAction({
      type: UPDATE_ITEM,
      updatedQuestion: {
        ...question,
        enableWhen: question?.enableWhen?.length || [{ operator: '', answerString: '', question: '' }],
      },
      pageIndex,
      questionIndex,
    });
  };

  const handleDisableDisplayLogic = () => {
    setIsDisplayLogicEnabled(false);
    let updatedQuestion = { ...question };
    delete updatedQuestion.enableWhen;

    dispatchQuestionnaireAction({
      type: UPDATE_ITEM,
      updatedQuestion,
      pageIndex,
      questionIndex,
    });
  };

  const handleEnableDescription = () => {
    setIsDescriptionEnabled(true);
    dispatchQuestionnaireAction({
      type: UPDATE_ITEM,
      updatedQuestion: {
        ...question,
        description: question?.description || '',
      },
      pageIndex,
      questionIndex,
    });
  };

  const handleDisableDescription = () => {
    setIsDescriptionEnabled(false);
    let updatedQuestion = {
      ...question,
      extension: question.extension.map((ext) =>
        ext.url.includes('Item/description') ? { ...ext, valueString: null } : ext,
      ),
    };
    delete updatedQuestion.description;

    dispatchQuestionnaireAction({
      type: UPDATE_ITEM,
      updatedQuestion: updatedQuestion,
      pageIndex,
      questionIndex,
    });
  };

  const handleEnableExplanation = () => {
    setIsExplanationEnabled(true);
    dispatchQuestionnaireAction({
      type: UPDATE_ITEM,
      updatedQuestion: {
        ...question,
        explanation: question?.explanation || { type: '', text: '' },
      },
      pageIndex,
      questionIndex,
    });
  };

  const handleDisableExplanation = () => {
    setIsExplanationEnabled(false);
    let updatedQuestion = {
      ...question,
      extension: question.extension.map((ext) =>
        ext.url.includes('explanation') ? { ...ext, valueString: null } : ext,
      ),
    };
    delete updatedQuestion.explanation;

    dispatchQuestionnaireAction({
      type: UPDATE_ITEM,
      updatedQuestion,
      pageIndex,
      questionIndex,
    });
  };

  const handleMenuIconClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCreateAndUpdateQuestion = (pageIndex, questionIndex, updatedQuestion) => {
    debounceFunction(
      () => dispatchQuestionnaireAction({ type: UPDATE_ITEM, updatedQuestion, pageIndex, questionIndex }),
      500,
    );
  };

  const handleDuplicateQuestion = (pageIndex, questionIndex) => {
    const nextItemLinkId = generateItemLinkId(questionnaireState);
    dispatchQuestionnaireAction({
      type: DUPLICATE_ITEM,
      pageIndex,
      questionIndex,
      nextItemLinkId,
      updateLastQuestionLinkId,
    });
  };

  const handleCreateQuestionTemplate = (questionType) => {
    if (!questionType) return;

    let { linearConfig, sliderConfig, ...cleanQuestion } = question;

    const newId = generateShortUuid();

    let updatedQuestion = { ...cleanQuestion, ...questionTemplates[questionType] };

    updatedQuestion.linkId = question.linkId;

    if (updatedQuestion.type === 'group') {
      updatedQuestion.id = `complex-${newId}`;
      updatedQuestion.item = [
        {
          id: generateShortUuid(),
          type: 'group',
          item: [
            {
              id: generateShortUuid(),
              linkId: generateShortUuid(),
              text: '',
              type: '',
              extension: [
                {
                  url: 'Item/complex-value-attribute-sequence',
                  valueInteger: 1,
                },
              ],
            },
          ],
        },
      ];
    } else {
      updatedQuestion.id = newId;

      if (updatedQuestion.type === 'choice') {
        updatedQuestion.answerOption = [
          {
            valueCoding: {
              id: 0,
              sequence: 1,
              display: '',
              code: 1,
              extension: [
                {
                  url: 'Item/AnswerOption/ValueCoding/sequence-value',
                  valueInteger: 1,
                },
              ],
            },
          },
        ];
      }
    }

    dispatchQuestionnaireAction({
      type: UPDATE_ITEM,
      updatedQuestion,
      pageIndex,
      questionIndex,
    });
  };

  const handleQuestionTypeDropdown = (event) => {
    const selectedType = event.target.value;
    handleQuestionValidationErrors({ ...questionErrors, [question.linkId]: {} });
    setSelectedQuestionType(event.target.value);
    handleCreateQuestionTemplate(event.target.value.type);
  };

  const handleReportVisibilityChange = (event) => {
    setShowInReportEnabled(event.target.checked);
    let newQuestion = structuredClone(question);

    newQuestion.extension = newQuestion?.extension?.map((extension) => {
      if (extension.url === 'Item/hide-question') {
        const newExtension = { ...extension, valueBoolean: !event.target.checked };
        return newExtension;
      }
      return extension;
    });

    handleCreateAndUpdateQuestion(pageIndex, questionIndex, newQuestion);
  };

  const handleQuestionRequiredChange = (event) => {
    setIsRequiredEnabled(event.target.checked);
    const newQuestion = structuredClone(question);
    newQuestion.required = event.target.checked;
    handleCreateAndUpdateQuestion(pageIndex, questionIndex, newQuestion);
  };

  const getQuestionType = () => {
    let Page;
    if (isLinearScaleQuestion(question)) {
      Page = LinearScale;
    } else if (isNumericSliderQuestion(question)) {
      Page = NumericSlider;
    } else if (isBodyDiagramQuestion(question)) {
      Page = BodyDiagram;
    } else if (isDropdownQuestion(question)) {
      Page = Dropdown;
    } else if (isLargeButtonQuestion(question)) {
      Page = LargeButton;
    } else if (isRadioButtonQuestion(question)) {
      Page = RadioButton;
    } else if (isCheckboxQuestion(question)) {
      Page = Checkboxes;
    } else if (question.type === 'text') {
      const TEXT_QUESTION_ALLOWED_LENGTH = 255;
      const maxAllowedLengthExtension = extractExtension(question.extension, 'Item/max-length');

      Page = maxAllowedLengthExtension?.valueInteger > TEXT_QUESTION_ALLOWED_LENGTH ? Paragraph : Text;
    } else if (question.type === 'integer' && isIntegerOnlyQuestion(question)) {
      Page = Integer;
    } else if (question.type === 'decimal') {
      Page = Number;
    } else if (question.type.includes('date')) {
      Page = DatePicker;
    } else if (question.type.includes('group')) {
      Page = DataGrid;
    } else if (question.type === 'display') {
      Page = Display;
    } else {
      return <></>;
    }

    return (
      <Page
        pageIndex={pageIndex}
        questionIndex={questionIndex}
        handleCreateQuestion={handleCreateAndUpdateQuestion}
        question={question}
        DescriptionComponent={
          isDescriptionEnabled && (
            <Box sx={{ mt: 2, mb: 1 }}>
              <Description
                question={question}
                handleDescriptionCallback={handleDescription}
                descriptionExtension={question.extension}
                handleDisableDescription={handleDisableDescription}
              />
            </Box>
          )
        }
      />
    );
  };

  const handleExplanation = (explanationExtension, explanation) => {
    const newQuestion = structuredClone(question);
    let newExtensions = newQuestion.extension.filter((extension) => !extension.url.includes('explanation'));
    newExtensions = [...newExtensions, ...explanationExtension];

    newQuestion.extension = newExtensions;
    newQuestion.explanation = explanation;
    handleCreateAndUpdateQuestion(pageIndex, questionIndex, newQuestion);
  };

  const handleDescription = (descriptionExtension, description) => {
    const newQuestion = structuredClone(question);
    let newExtensions = newQuestion.extension.filter((extension) => !extension.url.includes('description'));
    newExtensions = [...newExtensions, descriptionExtension];

    newQuestion.extension = newExtensions;
    newQuestion.description = description;
    handleCreateAndUpdateQuestion(pageIndex, questionIndex, newQuestion);
  };

  const handleDisplayCondition = (displayConditions) => {
    const newQuestion = structuredClone(question);

    newQuestion.enableWhen = displayConditions;
    handleCreateAndUpdateQuestion(pageIndex, questionIndex, newQuestion);
  };

  const handleClose = async () => {
    const validationResult = await validateQuestion(question);
    console.log('validationResult', validationResult);
    handleQuestionValidationErrors({ ...questionErrors, [question.linkId]: validationResult });
    if (Object.keys(validationResult).length > 0) {
      console.log(validationResult);
    } else {
      // setIsEditMode(false);
      dispatchQuestionnaireAction({
        type: CLOSE_EDIT_MODE,
        pageIndex,
        questionIndex,
      });
    }
  };

  const questionEditMenuItems = [
    {
      id: 0,
      label: strings.addDescription,
      icon: <Add sx={{ mr: 1 }} fontSize="small" />,
      handleClick: handleEnableDescription,
      show: true,
    },
    {
      id: 1,
      label: strings.addExplanation,
      icon: <Add sx={{ mr: 1 }} fontSize="small" />,
      handleClick: handleEnableExplanation,
      show: true,
    },
    {
      id: 2,
      label: strings.addDisplayLogic,
      icon: <AccountTree sx={{ mr: 1 }} fontSize="small" />,
      handleClick: handleEnableDisplayLogic,
      show: true,
    },
    {
      id: 3,
      label: strings.duplicate,
      icon: <FileCopy sx={{ mr: 1 }} fontSize="small" />,
      handleClick: () => handleDuplicateQuestion(pageIndex, questionIndex),
      show: true,
    },
  ];
  console.log('question', question);
  return (
    <>
      <Typography>
        <FormLabel required={true} sx={{ color: '#000' }}>
          {strings.itemType}
        </FormLabel>
      </Typography>
      <FormControl fullWidth size="small" sx={{ width: { xs: '100%', md: '500px' }, mt: 1, mb: 2 }}>
        <Select
          value={selectedQuestionType}
          onChange={handleQuestionTypeDropdown}
          displayEmpty
          renderValue={(selectedValue) => selectedValue?.label || placeholders.selectItemType}
        >
          {createMenuItems(questionTypeOptions)}
        </Select>
      </FormControl>
      {getQuestionType()}
      {isExplanationEnabled && (
        <Box sx={{ mt: 4, mb: 2 }}>
          <Explanation
            question={question}
            handleExplanation={handleExplanation}
            explanationExtension={question.extension}
            handleDisableExplanation={handleDisableExplanation}
          />
        </Box>
      )}
      {isDisplayLogicEnabled && (
        <>
          <Divider sx={{ mt: 4, mb: 3 }} />
          {/* Display Condition UI */}
          <DisplayLogic
            question={question}
            pageIndex={pageIndex}
            questionIndex={questionIndex}
            handleDisplayCondition={handleDisplayCondition}
            handleDisableDisplayLogic={handleDisableDisplayLogic}
          />
        </>
      )}
      {selectedQuestionType.type && (
        <>
          <Divider sx={{ mt: 3 }} />
          <Stack direction="row" justifyContent="space-between" sx={{ mt: 2 }}>
            <Button variant="contained" onClick={handleClose}>
              {strings.close}
            </Button>
            <Stack direction="row" alignItems="center" spacing={2}>
              {/* <FormControlLabel
                value={strings.report}
                control={
                  <Switch
                    size="small"
                    color="primary"
                    checked={showInReportEnabled}
                    onChange={handleReportVisibilityChange}
                  />
                }
                label={strings.report}
                labelPlacement="start"
              /> */}
              <FormControlLabel
                control={
                  <Checkbox
                    size="small"
                    color="primary"
                    checked={showInReportEnabled}
                    onChange={handleReportVisibilityChange}
                  />
                }
                label={strings.report}
                labelPlacement="end"
              />
              {/* {question.type !== 'display' && question.type !== 'group' && (
                <FormControlLabel
                  value={strings.required}
                  control={
                    <Switch
                      size="small"
                      color="primary"
                      checked={question?.required}
                      onChange={handleQuestionRequiredChange}
                    />
                  }
                  label={strings.required}
                  labelPlacement="start"
                />
              )} */}
              {question.type !== 'display' && question.type !== 'group' && (
                <FormControlLabel
                  value={strings.required}
                  control={
                    <Checkbox
                      size="small"
                      color="primary"
                      checked={isRequiredEnabled}
                      onChange={handleQuestionRequiredChange}
                    />
                  }
                  label={strings.required}
                  labelPlacement="end"
                />
              )}
              <MoreVert onClick={handleMenuIconClick} sx={{ cursor: 'pointer' }} />
              <MenuList menuItems={questionEditMenuItems} anchorEl={anchorEl} setAnchorEl={setAnchorEl} />
            </Stack>
          </Stack>
        </>
      )}
    </>
  );
};
