import React from 'react';
import { strings } from '@/utility/strings';
import { Box, Stack } from '@mui/material';
import { QuestionText } from '../components/QuestionText';

export const Display = (props) => {
  const { question, DescriptionComponent } = props;

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
      </Stack>
    </Box>
  );
};
