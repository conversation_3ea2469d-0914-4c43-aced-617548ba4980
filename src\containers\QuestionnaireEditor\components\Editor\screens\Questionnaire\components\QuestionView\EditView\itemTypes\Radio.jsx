import React, { useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, FormControlLabel, Checkbox } from '@mui/material';
import { useState } from 'react';
import { MultipleResponseOptions } from './components/MultipleResponseOptions';
import { QuestionTextInput } from './components/QuestionTextInput';
import { Add } from '@mui/icons-material';
import { strings } from '@/utility/strings';
import { characterLength } from '@/containers/CommonConstants';

export const RadioButton = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;
  const [questionText, setQuestionText] = useState(question?.text || '');

  const [answerOption, setAnswerOptions] = useState(
    question?.answerOption?.length
      ? question.answerOption
      : [
          {
            valueCoding: {
              id: 0,
              sequence: 1,
              display: '',
              code: 1,
              extension: [
                {
                  url: 'Item/AnswerOption/ValueCoding/sequence-value',
                  valueInteger: 1,
                },
              ],
            },
          },
        ],
  );

  const handleQuestionCreation = (question) => {
    handleCreateQuestion(pageIndex, questionIndex, { ...question, type: 'choice' });
  };

  const handleHorizontalOrientation = (event) => {
    let newQuestion = structuredClone(question);
    setHorizontalOrientation((previousOrientation) => !previousOrientation);

    newQuestion.extension = newQuestion?.extension?.map((extension) => {
      if (extension.url === 'Item/horizontal-orientation') {
        const newExtension = { ...extension, valueBoolean: event.target.checked };
        return newExtension;
      }
      return extension;
    });

    handleCreateQuestion(pageIndex, questionIndex, newQuestion);
  };
  const isHorizontal = useMemo(() => {
    const horizontalOrientationExtension = question.extension.find(
      (extension) => extension.url === 'Item/horizontal-orientation',
    );
    return horizontalOrientationExtension ? horizontalOrientationExtension.valueBoolean : false;
  }, [question.extension]);
  const [horizontalOrientation, setHorizontalOrientation] = useState(isHorizontal);

  const handleQuestionTextChange = (event) => {
    // setQuestionText(event.target.value);
    const newText = event.target.value.slice(0, characterLength.itemTextLength);
    setQuestionText(newText);
    let newQuestion = {
      ...question,
      text: event.target.value,
    };
    handleQuestionCreation(newQuestion);
  };

  const handleAddOption = () => {
    let options = [...answerOption];
    let newOption = {
      valueCoding: {
        id: options?.length ? options[options.length - 1]?.valueCoding.id + 1 : 0,
        sequence: options?.length ? options[options.length - 1]?.valueCoding.sequence + 1 : 1,
        display: '',
        code: options?.length ? options[options.length - 1]?.valueCoding?.code + 1 : 1,
        extension: [
          {
            url: 'Item/AnswerOption/ValueCoding/sequence-value',
            valueInteger: options?.length ? options[options.length - 1]?.valueCoding.sequence + 1 : 1,
          },
        ],
      },
    };
    options.push(newOption);
    setAnswerOptions(options);
  };

  const handleAddOtherOption = () => {
    let options = [...answerOption];
    let newOption = {
      valueCoding: {
        id: options?.length ? options[options.length - 1]?.valueCoding.id + 1 : 0,
        sequence: options?.length ? options[options.length - 1]?.valueCoding.sequence + 1 : 1,
        display: '',
        code: options?.length ? options[options.length - 1]?.valueCoding?.code + 1 : 1,
        extension: [
          {
            url: 'Item/AnswerOption/ValueCoding/sequence-value',
            valueInteger: options?.length ? options[options.length - 1]?.valueCoding.sequence + 1 : 1,
          },
          {
            url: 'Item/AnswerOption/ValueCoding/other-option',
            valueString: `id:,question:`,
          },
        ],
      },
    };
    options.push(newOption);
    setAnswerOptions(options);
  };

  const handleAnswerOptionChange = (keyName, keyValue, index) => {
    let options = structuredClone(answerOption);
    options[index].valueCoding[keyName] = keyValue;

    const otherOptionExtensionIndex = options[index].valueCoding.extension.findIndex(
      (ext) => ext.url === 'Item/AnswerOption/ValueCoding/other-option',
    );
    if (otherOptionExtensionIndex !== -1) {
      options[index].valueCoding.extension[otherOptionExtensionIndex].valueString =
        `id:${options[index]?.valueCoding?.id},question:${options[index]?.valueCoding?.display}`;
    }

    setAnswerOptions(options);

    const newQuestion = structuredClone(question);
    newQuestion.answerOption = options;

    handleQuestionCreation(newQuestion);
  };

  const handleRemoveAnswerOption = (optionIndex) => {
    let options = structuredClone(answerOption);
    options.splice(optionIndex, 1);
    setAnswerOptions(options);

    const newQuestion = structuredClone(question);
    newQuestion.answerOption = options;

    handleQuestionCreation(newQuestion);
  };

  return (
    <>
      <Stack>
        <QuestionTextInput
          value={questionText}
          onChange={handleQuestionTextChange}
          question={question}
          characterLimit={characterLength.itemTextLength}
        />
        {DescriptionComponent}
        <MultipleResponseOptions
          type="radio"
          answerOption={answerOption}
          handleAnswerOptionCallback={handleAnswerOptionChange}
          handleRemoveAnswerOption={handleRemoveAnswerOption}
          question={question}
        />

        <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
          <Button
            onClick={handleAddOption}
            startIcon={<Add fontSize="small" />}
            sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
          >
            Add Option
          </Button>
          <Button
            onClick={handleAddOtherOption}
            startIcon={<Add fontSize="small" />}
            sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
          >
            {' '}
            Add "Other" Option
          </Button>
          <FormControlLabel
            control={<Checkbox checked={horizontalOrientation} onChange={handleHorizontalOrientation} />}
            label={strings.horizontalOrientation}
            labelPlacement="end"
            sx={{
              color: '#4D76A9',
              '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
            }}
          />
        </Stack>
      </Stack>
    </>
  );
};
