import React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import { Box, TextField, FormControlLabel, Checkbox, Typography } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { format } from 'date-fns';
import { dateFormats, statuses, publishedStatuses } from '@/containers/CommonConstants';
import { CustomModal } from '@/components/CustomModal';
import { Close, Preview, Visibility, Book, PublishedWithChanges } from '@mui/icons-material';
import LaunchIcon from '@mui/icons-material/Launch';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';
import { MenuList } from '@/components';
import { strings } from '@/utility/strings';

const getDataGridColumns = (
  handleClick,
  handlePublishModal,
  handleDeleteModal,
  handleEditQuestionnaire,
  handleViewQuestionnaire,
  handlePreviewQuestionnaire,
  handleDuplicateQuestionnaire,
  handleExportQuestionnaire,
  handleDownloadCodebook,
) => [
  {
    field: 'name',
    headerName: 'Name',
    minWidth: 350,
    flex: 1,
    sortable: true,
    hideable: false,
    renderCell: (params) => {
      const { row } = params;
      return row.name;
    },
  },
  {
    field: 'createdDate',
    headerName: 'Created',
    width: 150,
    sortable: true,
    renderCell: (params) => format(new Date(params.value), dateFormats.year_month_date),
  },
  {
    field: 'modifiedDate',
    headerName: 'Modified',
    width: 150,
    sortable: true,
    renderCell: (params) => format(new Date(params.value), dateFormats.year_month_date),
  },
  {
    field: 'contentStatus',
    headerName: 'Status',
    width: 130,
    sortable: true,
    renderCell: (params) => {
      return params.value === statuses.draft ? strings.draft : strings.final;
    },
  },
  {
    field: 'publishStatus',
    headerName: 'Published',
    width: 130,
    sortable: true,
    renderCell: (params) => params.value.toUpperCase(),
  },
  {
    field: 'Action',
    headerName: '',
    width: 80,
    sortable: false,
    disableColumnMenu: true,
    hideable: false,
    renderCell: (params) => (
      <MoreVertIcon
        data-questionnaireid={params.row.questionnaireId}
        data-questionnairestatus={params.row.contentStatus}
        data-publishstatus={params.row.publishStatus}
        onClick={handleClick}
        sx={{ cursor: 'pointer', display: 'block', color: 'rgba(0, 0, 0, 0.54)' }}
      />
    ),
  },
];

export const QuestionnaireTable = (props) => {
  const {
    questionnaireList = [],
    handlePublishQuestionnaire,
    handlePreviewQuestionnaire,
    handleViewQuestionnaire,
    handleEditQuestionnaire,
    handleDownloadCodebook,
    handleDuplicateQuestionnaire,
    handleDeleteQuestionnaire,
    handleExportQuestionnaire,
  } = props;

  // Get DataGrid settings from localStorage
  const getDataGridStateFromStorage = () => {
    try {
      const storedState = localStorage.getItem('questionnaireDataGridState');
      return storedState ? JSON.parse(storedState) : {};
    } catch {
      return {};
    }
  };

  const searchFieldRef = useRef(null);
  const [searchText, setSearchText] = useState('');
  const [dataGridState, setDataGridState] = useState(getDataGridStateFromStorage());
  const [anchorEl, setAnchorEl] = useState(null);
  const [openPublishModal, setOpenPublishModal] = useState(false);
  const [publishStatus, setPublishStatus] = useState({ isPrivate: false, isPublic: false });
  const [questionnaireId, setQuestionnaireId] = useState('');
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [publishedRepository, setPublishedRepository] = useState('');

  // Save DataGrid state to localStorage
  const saveDataGridState = useCallback((newState) => {
    setDataGridState(newState);
    localStorage.setItem('questionnaireDataGridState', JSON.stringify(newState));
  }, []);

  const handlePublishModal = (questionnaireId, publishRepository) => {
    setOpenPublishModal(true);
    setQuestionnaireId(questionnaireId);
    if (publishRepository === 'private') {
      setPublishStatus({ isPrivate: true, isPublic: false });
    } else if (publishRepository === 'public') {
      setPublishStatus({ isPrivate: false, isPublic: true });
    } else if (publishRepository === 'both') {
      setPublishStatus({ isPrivate: true, isPublic: true });
    } else {
      setPublishStatus({ isPrivate: false, isPublic: false });
    }
  };

  const handleDeleteModal = (questionnaireId, publishRepository) => {
    setOpenDeleteConfirmation(true);
    setQuestionnaireId(questionnaireId);
    setPublishedRepository(publishRepository);
  };

  const handleDeleteConfirm = () => {
    handleDeleteQuestionnaire(questionnaireId, publishedRepository);
    setOpenDeleteConfirmation(false);
  };

  const handlePublishConfirm = () => {
    let publishedStatus = '';
    if (publishStatus.isPrivate && !publishStatus.isPublic) {
      publishedStatus = publishedStatuses.private;
    } else if (!publishStatus.isPrivate && publishStatus.isPublic) {
      publishedStatus = publishedStatuses.public;
    } else if (publishStatus.isPrivate && publishStatus.isPublic) {
      publishedStatus = publishedStatuses.both;
    } else if (!publishStatus.isPrivate && !publishStatus.isPublic) {
      publishedStatus = publishedStatuses.no;
    }
    handlePublishQuestionnaire(questionnaireId, publishedStatus, publishedRepository);
    setOpenPublishModal(false);
  };

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const rows = useMemo(() => {
    if (!questionnaireList?.length) return [];

    return questionnaireList.map((questionnaire) => ({
      id: questionnaire?.artifactId,
      name: questionnaire?.shortName,
      title: questionnaire?.title,
      modifiedDate: questionnaire?.modifiedDate,
      createdDate: questionnaire?.createdDate || questionnaire?.modifiedDate, // Fallback to modifiedDate if createdDate not available
      contentStatus: questionnaire?.contentStatus,
      publishStatus: questionnaire?.publishStatus,
      questionnaireId: questionnaire?.artifactId,
    }));
  }, [questionnaireList]);

  const filteredRows = useMemo(() => {
    if (!searchText) return rows;

    const searchLower = searchText.toLowerCase();
    return rows.filter(
      (row) =>
        row.name.toLowerCase().includes(searchLower) ||
        (row.contentStatus.toLowerCase() === statuses.final && strings.final.toLowerCase()?.includes(searchLower)) ||
        (row.contentStatus.toLowerCase() === statuses.draft && strings.draft.toLowerCase()?.includes(searchLower)),
    );
  }, [rows, searchText]);

  const handleQuestionnaireSearch = useCallback((e) => {
    const searchString = e?.target?.value || '';
    setSearchText(searchString);
  }, []);

  const handleClearSearchKey = useCallback(() => {
    setSearchText('');
    if (searchFieldRef.current) {
      searchFieldRef.current.value = '';
    }
  }, []);

  const columns = useMemo(
    () =>
      getDataGridColumns(
        handleClick,
        handlePublishModal,
        handleDeleteModal,
        handleEditQuestionnaire,
        handleViewQuestionnaire,
        handlePreviewQuestionnaire,
        handleDuplicateQuestionnaire,
        handleExportQuestionnaire,
        handleDownloadCodebook,
      ),
    [
      handleEditQuestionnaire,
      handleViewQuestionnaire,
      handlePreviewQuestionnaire,
      handleDuplicateQuestionnaire,
      handleExportQuestionnaire,
      handleDownloadCodebook,
    ],
  );

  const menuItems = [
    {
      id: 0,
      label: strings.edit,
      icon: <LaunchIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () =>
        handleEditQuestionnaire(
          anchorEl?.getAttribute('data-questionnaireid'),
          anchorEl?.getAttribute('data-questionnairestatus'),
        ),
      show: anchorEl?.getAttribute('data-questionnairestatus') === statuses.draft,
    },
    {
      id: 1,
      label: strings.view,
      icon: <Preview sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () =>
        handleViewQuestionnaire(
          anchorEl?.getAttribute('data-questionnaireid'),
          anchorEl?.getAttribute('data-publishstatus'),
        ),
      show: anchorEl?.getAttribute('data-questionnairestatus') === statuses.final,
    },
    {
      id: 2,
      label: strings.preview,
      icon: <Visibility sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () =>
        handlePreviewQuestionnaire(
          anchorEl?.getAttribute('data-questionnaireid'),
          anchorEl?.getAttribute('data-publishstatus'),
        ),
      show: true,
    },
    {
      id: 3,
      label: strings.duplicate,
      icon: <FileCopyIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () =>
        handleDuplicateQuestionnaire(
          anchorEl?.getAttribute('data-questionnaireid'),
          anchorEl?.getAttribute('data-publishstatus'),
        ),
      show: true,
    },
    {
      id: 4,
      label: strings.export,
      icon: <SaveIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () =>
        handleExportQuestionnaire(
          anchorEl?.getAttribute('data-questionnaireid'),
          anchorEl?.getAttribute('data-publishstatus'),
        ),
      show: true,
    },
    {
      id: 5,
      label: strings.codeBook,
      icon: <Book sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () =>
        handleDownloadCodebook(
          anchorEl?.getAttribute('data-questionnaireid'),
          anchorEl?.getAttribute('data-publishstatus'),
          '', // we don't have access to code book html at this place, parent needs to retrieve if from questionnaire
        ),
      show: true,
    },
    {
      id: 6,
      label: strings.publish,
      icon: <PublishedWithChanges sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () =>
        handlePublishModal(
          anchorEl?.getAttribute('data-questionnaireid'),
          anchorEl?.getAttribute('data-publishstatus'),
        ),
      show: anchorEl?.getAttribute('data-questionnairestatus') === statuses.final,
    },
    {
      id: 7,
      label: strings.delete,
      icon: <DeleteIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () =>
        handleDeleteModal(anchorEl?.getAttribute('data-questionnaireid'), anchorEl?.getAttribute('data-publishstatus')),
      show: true,
    },
  ];

  const publishModalContent = (
    <>
      <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
        <FormControlLabel
          control={
            <Checkbox
              checked={publishStatus.isPrivate}
              onChange={(e) => setPublishStatus({ ...publishStatus, isPrivate: e.target.checked })}
            />
          }
          label={strings.private}
          sx={{ mr: 4 }}
        />
        <FormControlLabel
          control={
            <Checkbox
              checked={publishStatus.isPublic}
              onChange={(e) => setPublishStatus({ ...publishStatus, isPublic: e.target.checked })}
            />
          }
          label={strings.public}
        />
      </Box>
    </>
  );

  return (
    <>
      <Box sx={{ mb: 2 }}>
        <TextField
          autoComplete="off"
          size="small"
          onChange={handleQuestionnaireSearch}
          placeholder={strings.search}
          inputRef={searchFieldRef}
          sx={{ width: { md: 250 }, maxWidth: '100%' }}
          InputProps={{
            endAdornment: searchText && (
              <Close onClick={handleClearSearchKey} sx={{ color: 'grey', cursor: 'pointer' }} />
            ),
          }}
        />
      </Box>
      {questionnaireList?.length && filteredRows?.length ? (
        <Box sx={{ width: '100%' }}>
          <DataGrid
            rows={filteredRows}
            columns={columns}
            initialState={{
              sorting: {
                sortModel: dataGridState.sortModel || [{ field: 'name', sort: 'asc' }],
              },
              columns: {
                columnVisibilityModel: dataGridState.columnVisibilityModel || {
                  createdDate: false,
                },
              },
              pagination: {
                paginationModel: { pageSize: 15 },
              },
            }}
            onSortModelChange={(sortModel) => {
              saveDataGridState({
                ...dataGridState,
                sortModel,
              });
            }}
            onColumnVisibilityModelChange={(columnVisibilityModel) => {
              saveDataGridState({
                ...dataGridState,
                columnVisibilityModel,
              });
            }}
            disableRowSelectionOnClick
            disableColumnFilter
            disableDensitySelector
            disableCellFocus
            sortingOrder={['asc', 'desc']}
            pageSizeOptions={[10, 15, 25, 50]}
            paginationMode="client"
            disableColumnResize={true}
            sx={{
              borderRadius: 1,
              '& .MuiDataGrid-cell, & .MuiDataGrid-columnHeader': {
                py: '0.6rem',
                px: 2,
                outline: 'none !important',
                display: 'flex',
                alignItems: 'center',
              },
              '& .MuiDataGrid-cell': {
                fontSize: '0.9rem',
              },
              '& .MuiDataGrid-columnHeader': {
                fontSize: '0.9rem',
                fontWeight: 700,
              },
            }}
          />
        </Box>
      ) : (
        <Typography>No Questionnaires found</Typography>
      )}
      <MenuList menuItems={menuItems} anchorEl={anchorEl} setAnchorEl={setAnchorEl} />
      <CustomModal
        open={openPublishModal}
        onClose={() => setOpenPublishModal(false)}
        onConfirm={handlePublishConfirm}
        title={strings.publishConfirmation}
        subTitle={strings.selectRepositoryYouWantToPublishIn}
        content={publishModalContent}
        saveButtonText={strings.publish}
        closeButtonText={strings.cancel}
      />
      <CustomModal
        open={openDeleteConfirmation}
        onClose={() => setOpenDeleteConfirmation(false)}
        onConfirm={handleDeleteConfirm}
        title={strings.deleteQuestionnaire}
        subTitle={strings.thisActionCanNotBeUndone}
        saveButtonText={strings.delete}
        closeButtonText={strings.cancel}
      />
    </>
  );
};
