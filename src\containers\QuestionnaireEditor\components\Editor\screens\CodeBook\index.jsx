import React, { useState, useContext } from 'react';
import { TextField, Typography, Paper, Stack, IconButton, Box, Modal } from '@mui/material';
import { Code } from '@mui/icons-material';
import { CambianTooltip } from '@/components';
import { Help } from '@mui/icons-material';
import { modes } from '@/containers/CommonConstants';
import { strings } from '@/utility/strings';
import { ShowCodebookTemplateFile } from './ShowCodebookTemplateFile';
import { headerTemplate, questionnaireDetailsTemplate } from '@/utility/codeBookUtil';
import { QuestionnaireContext, QuestionnaireDetailsContext, QuestionnaireReportContext } from '@/context';
import { useDebouncing } from '@/hooks/useDebouncing';

export const CodeBook = (props) => {
  const { codebookDefaultTemplate } = props;
  const { questionnaireReport, handleQuestionnaireReportContext } = useContext(QuestionnaireReportContext);

  const { debounceFunction } = useDebouncing();
  const [mode, setMode] = useState(modes.editor);
  const [openTemplateReportFile, setOpenTemplateReportFile] = useState(false);
  const [codeBookEditorTemplate, setCodeBookEditorTemplate] = useState(
    questionnaireReport?.codeBookHtml || codebookDefaultTemplate,
  );
  const { questionnaireState } = useContext(QuestionnaireContext);
  const { questionnaireDetails } = useContext(QuestionnaireDetailsContext);

  const ModalStyle = {
    margin: 'auto',
    marginTop: '20px',
    maxWidth: '80%',
    borderRadius: '10px',
    maxHeight: '80vh',
    overflowY: 'scroll',
    // '&::-webkit-scrollbar': { display: 'none' },
  };
  const onClose = () => {
    setOpenTemplateReportFile(false);
  };

  const handleCodeBookEditorChange = (e) => {
    setCodeBookEditorTemplate(e.target.value);
    debounceFunction(() => handleQuestionnaireReportContext({ codeBookHtml: e.target.value }), 300);
  };

  const syntaxesToReplace = [
    '{Questionnaire.title}',
    '{Questionnaire.id}',
    '{Questionnaire.mappedQuestionsList}',
    '{Questionnaire.questionnaireColumns}',
    '{Questionnaire.mappedResponseList}',
  ];

  const mappedQuestions = () => {
    if (questionnaireState[0]?.item?.length) {
      let serialNumber = 1;
      const tableElement = document.createElement('table');
      tableElement.style.cssText = 'border-collapse: collapse; width: 900px;';
      const tbodyElement = document.createElement('tbody');
      tableElement.appendChild(tbodyElement);

      questionnaireState.forEach((questionnaireItem, stateIndex) => {
        questionnaireItem.item.forEach((item, itemIndex) => {
          const trElement = document.createElement('tr');
          const td1Element = document.createElement('td');
          td1Element.style.cssText = 'width: 714px; border-style: solid; padding-left: 10px;';
          td1Element.innerHTML = `<p>${item.text}</p>`;
          trElement.appendChild(td1Element);
          const td2Element = document.createElement('td');
          td2Element.style.cssText = 'width: 178px; border-style: solid; padding-left: 10px;';
          td2Element.innerHTML = `<p>Item${serialNumber}</p>`;
          serialNumber++;
          trElement.appendChild(td2Element);
          tbodyElement.appendChild(trElement);
        });
      });

      return tableElement.outerHTML;
    }
    return '{Questionnaire.mappedQuestionsList}';
  };

  const generateColumnNames = () => {
    if (questionnaireState[0]?.item?.length) {
      let serialNumber = 1;
      const questionNumbers = questionnaireState
        .flatMap((page) => page.item.map((_, index) => `Item${serialNumber++}`))
        .join(', ');
      return `${questionnaireDetails?.id || '{Questionnaire.id}'}, ${questionNumbers}, S1`;
    }
    return '{Questionnaire.questionnaireColumns}';
  };

  const mappedResponseOptions = () => {
    if (questionnaireState[0]?.item?.length) {
      let serialNumber = 1;
      const containerElement = document.createElement('div');

      questionnaireState.forEach((questionnaireItem, stateIndex) => {
        questionnaireItem.item.forEach((item, itemIndex) => {
          const questionHeading = document.createElement('p');
          questionHeading.style.cssText = 'font-weight: bold; margin-top: 10px;';
          questionHeading.textContent = `Responses for Item${serialNumber}:`;
          serialNumber++;
          containerElement.appendChild(questionHeading);

          const questionTable = document.createElement('table');
          questionTable.style.cssText = 'border-collapse: collapse; width: 900px;';
          const tbodyElement = document.createElement('tbody');
          questionTable.appendChild(tbodyElement);

          if (item.type === 'choice' && item.answerOption && item.answerOption.length > 0) {
            const answerOptions = item.answerOption;

            answerOptions.forEach((option, optionIndex) => {
              const trElement = document.createElement('tr');
              const td1Element = document.createElement('td');
              td1Element.style.cssText = 'width: 714px; border-style: solid; padding-left: 10px;';
              td1Element.innerHTML = `<p>${option.valueCoding.display}</p>`;
              trElement.appendChild(td1Element);

              const td2Element = document.createElement('td');
              td2Element.style.cssText = 'width: 178px; border-style: solid; padding-left: 10px;';
              td2Element.innerHTML = `<p>${optionIndex + 1}</p>`;
              trElement.appendChild(td2Element);

              tbodyElement.appendChild(trElement);
            });
          } else {
            const trElement = document.createElement('tr');
            const tdElement = document.createElement('td');
            tdElement.style.cssText = 'width: 892px; border-style: solid; padding-left: 10px;';
            tdElement.innerHTML = `<p>${item.type}</p>`;
            trElement.appendChild(tdElement);

            tbodyElement.appendChild(trElement);
          }

          questionTable.appendChild(tbodyElement);
          containerElement.appendChild(questionTable);
        });
      });

      return containerElement.outerHTML;
    }

    return '{Questionnaire.mappedResponseList}';
  };

  const replaceHtmlTemplateWithValues = () => {
    let replacedHtml = codeBookEditorTemplate;
    syntaxesToReplace.forEach((syntax) => {
      const key = syntax.replace(/({Questionnaire\.|})/g, '');
      const value = questionnaireDetails[key];
      if (value !== undefined && value !== null && value !== '') {
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), value);
      } else if (syntax === '{Questionnaire.mappedQuestionsList}') {
        const mappedQuestionsResult = mappedQuestions();
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), mappedQuestionsResult || '');
      } else if (syntax === '{Questionnaire.questionnaireColumns}') {
        const columnNames = generateColumnNames();
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), columnNames || '');
      } else if (syntax === '{Questionnaire.mappedResponseList}') {
        const mappedResponses = mappedResponseOptions();
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), mappedResponses || '');
      }
    });
    return replacedHtml;
  };

  return (
    <>
      <Box>
        <Paper sx={{ border: 'none' }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Stack direction="flex-start" alignItems="center">
              <Typography variant="h3">{strings.codeBook}</Typography>
              <CambianTooltip title={strings.codebookHelpDocument}>
                <IconButton color="primary" onClick={() => setOpenTemplateReportFile(true)}>
                  <Help
                    sx={{
                      fontSize: '20px',
                      fontWeight: 'normal',
                      color: '#0000008A',
                    }}
                  />
                </IconButton>
              </CambianTooltip>
            </Stack>
            <CambianTooltip title={mode === modes.editor ? strings.switchToPreview : strings.switchToEditor}>
              <IconButton color="primary" onClick={() => setMode(mode === modes.editor ? modes.preview : modes.editor)}>
                <Code
                  fontSize="large"
                  sx={{
                    background: mode === modes.editor ? '#4d76a933' : 'disabled',
                    border: '1px solid #4D76A9',
                    borderRadius: '4px',
                    height: '2rem',
                    width: '3rem',
                  }}
                />
              </IconButton>
            </CambianTooltip>
          </Stack>
        </Paper>

        {mode === modes.editor ? (
          <TextField
            sx={{
              maxWidth: '100%',
              marginTop: 2,
              '& .MuiOutlinedInput-root': {
                maxWidth: '100% ',
              },
            }}
            id="outlined-multiline-flexible"
            value={codeBookEditorTemplate}
            onChange={(e) => handleCodeBookEditorChange(e)}
            multiline
            fullWidth
            rows={22}
          />
        ) : (
          <Paper sx={{ marginTop: 2, p: 2, minHeight: '470px' }}>
            <Typography
              dangerouslySetInnerHTML={{
                __html: `${replaceHtmlTemplateWithValues()}`,
              }}
            ></Typography>
          </Paper>
        )}
      </Box>
      <Modal open={openTemplateReportFile} onClose={() => setOpenTemplateReportFile(false)}>
        <Box sx={ModalStyle}>
          <ShowCodebookTemplateFile onClose={onClose} />
        </Box>
      </Modal>
    </>
  );
};
