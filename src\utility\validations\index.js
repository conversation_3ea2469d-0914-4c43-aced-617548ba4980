import * as yup from 'yup';
import { errors } from '../strings';

function checkUndefinedValues(obj) {
  let allUndefined = true;
  for (const key in obj) {
    const value = obj[key];
    if (value !== undefined) {
      allUndefined = false;
      return allUndefined;
    }
  }

  return allUndefined;
}

const enableWhenSchema = yup.object().shape({
  question: yup.string().required(errors.requiredField),
  operator: yup.string().required(errors.requiredField),
  answerString: yup.string().required(errors.requiredField),
});

const numericValidationRuleSchema = yup
  .object()
  .shape({
    operator: yup.string().notRequired(),
    'min-value': yup.string().notRequired(),
    'max-value': yup.string().notRequired(),
    'min-exclusion': yup.string().notRequired(),
    'max-exclusion': yup.string().notRequired(),
  })
  .test('validationRule', 'Invalid validation rule', function (value) {
    const { operator, 'min-value': minValue, 'max-value': maxValue } = value;
    const validationErrors = [];
    if (operator && ['>', '>=', '<<'].includes(operator) && !minValue) {
      validationErrors.push(this.createError({ path: 'validationRule.min-value', message: errors.requiredField }));
    }
    if (operator && ['<', '<=', '<<'].includes(operator) && !maxValue) {
      validationErrors.push(this.createError({ path: 'validationRule.max-value', message: errors.requiredField }));
    }
    if ((minValue || maxValue) && !operator) {
      validationErrors.push(this.createError({ path: 'validationRule.operator', message: errors.requiredField }));
    }
    if (validationErrors.length > 0) {
      throw new yup.ValidationError(validationErrors);
    }
    return true; // return true if validation succeeds
  });

const sliderConfigSchema = yup
  .object()
  .shape({
    operator: yup.string().notRequired(),
    'min-value': yup
      .string()
      .notRequired()
      .test('lessThanMax', errors.minValueMustBeLess, function (minValue) {
        const { 'max-value': maxValue } = this.parent;
        if (minValue !== undefined && maxValue !== undefined) {
          return Number(minValue) < Number(maxValue);
        }
        return true;
      }),
    'max-value': yup
      .string()
      .notRequired()
      .test('greaterThanMin', errors.maxValueMustBeHigher, function (maxValue) {
        const { 'min-value': minValue } = this.parent;
        if (maxValue !== undefined && minValue !== undefined) {
          return Number(maxValue) > Number(minValue);
        }
        return true;
      }),
    'min-label': yup.string().notRequired(),
    'max-label': yup.string().notRequired(),
  })
  .test('sliderConfig', 'invalid slider configurations', function (value) {
    const validationErrors = [];
    if (value && Object.keys(value).length === 0) {
      return true;
    }

    const allUndefined = checkUndefinedValues(value);
    if (allUndefined) {
      return true; // not a numeric slider question, pass true
    }

    if (value) {
      if (!value['min-value']) {
        validationErrors.push(this.createError({ path: 'sliderConfig.min-value', message: errors.requiredField }));
      }
      if (!value['min-label']) {
        validationErrors.push(this.createError({ path: 'sliderConfig.min-label', message: errors.requiredField }));
      }
      if (!value['max-value']) {
        validationErrors.push(this.createError({ path: 'sliderConfig.max-value', message: errors.requiredField }));
      }
      if (!value['max-label']) {
        validationErrors.push(this.createError({ path: 'sliderConfig.max-label', message: errors.requiredField }));
      }
      if (Number(value['min-value']) === Number(value['max-value'])) {
        validationErrors.push(
          this.createError({ path: 'sliderConfig.min-value', message: errors.lowerAndHigherRangeCanNotBeSame }),
        );
        validationErrors.push(
          this.createError({ path: 'sliderConfig.max-value', message: errors.lowerAndHigherRangeCanNotBeSame }),
        );
      }
      if (validationErrors.length > 0) {
        throw new yup.ValidationError(validationErrors);
      }
    }

    return true;
  });

const linearConfigSchema = yup
  .object()
  .shape({
    operator: yup.string().notRequired(),
    'start-value': yup.string().notRequired(),
    'end-value': yup.string().notRequired(),
    'start-label': yup.string().notRequired(),
    'end-label': yup.string().notRequired(),
  })
  .test('linearConfig', 'invalid linear configurations', function (value) {
    const validationErrors = [];
    if (value && Object.keys(value).length === 0) {
      return true;
    }

    const allUndefined = checkUndefinedValues(value);
    if (allUndefined) {
      return true; // not a linear scale question, pass true
    }

    if (value) {
      if (!value['start-value']) {
        validationErrors.push(this.createError({ path: 'linearConfig.start-value', message: errors.requiredField }));
      }
      if (!value['start-label']) {
        validationErrors.push(this.createError({ path: 'linearConfig.start-label', message: errors.requiredField }));
      }
      if (!value['end-value']) {
        validationErrors.push(this.createError({ path: 'linearConfig.end-value', message: errors.requiredField }));
      }
      if (!value['end-label']) {
        validationErrors.push(this.createError({ path: 'linearConfig.end-label', message: errors.requiredField }));
      }
      if (value['start-value'] && value['end-value']) {
        if (Number(value['start-value']) > Number(value['end-value'])) {
          validationErrors.push(
            this.createError({ path: 'linearConfig.start-value', message: errors.lowerRangeSmaller }),
          );
          validationErrors.push(
            this.createError({ path: 'linearConfig.end-value', message: errors.higherRangeGreater }),
          );
        } else if (Number(value['start-value']) === Number(value['end-value'])) {
          validationErrors.push(
            this.createError({ path: 'linearConfig.start-value', message: errors.lowerAndHigherRangeCanNotBeSame }),
          );
          validationErrors.push(
            this.createError({ path: 'linearConfig.end-value', message: errors.lowerAndHigherRangeCanNotBeSame }),
          );
        }
      }
      if (validationErrors.length > 0) {
        throw new yup.ValidationError(validationErrors);
      }
    }

    return true;
  });

const gridColumnSchema = yup.object().shape({
  id: yup.string().notRequired(),
  text: yup.string().required(errors.requiredField),
  type: yup.string().required(errors.requiredField),
});

const explanationSchema = yup
  .object()
  .shape({
    text: yup.string().notRequired(),
    type: yup.string().notRequired(),
  })
  .test('explanation', 'explanation required', function (value) {
    const validationErrors = [];
    if (value && Object.keys(value).length === 0) {
      return true; // explanation not selected, pass validation
    }

    if (value && value.type === undefined && value.text === undefined) {
      return true; // explanation not selected, pass validation
    }

    if (!value?.type) {
      validationErrors.push(this.createError({ path: 'explanation.type', message: errors.requiredField }));
    }
    if (!value?.text) {
      validationErrors.push(this.createError({ path: 'explanation.text', message: errors.requiredField }));
    }
    if (validationErrors.length > 0) {
      throw new yup.ValidationError(validationErrors);
    }
    return true;
  });

export const questionSchemas = {
  display: yup.object().shape({
    text: yup.string().required(errors.requiredField),
    enableWhen: yup.array().of(enableWhenSchema).notRequired(),
    description: yup.mixed().test('description', errors.requiredField, function (value) {
      if (this.parent.hasOwnProperty('description') && !value) {
        return false;
      }
      return true;
    }),
    explanation: explanationSchema.notRequired(),
  }),
  text: yup.object().shape({
    text: yup.string().required(errors.requiredField),
    enableWhen: yup.array().of(enableWhenSchema).notRequired(),
    description: yup
      .mixed()
      .test('description', errors.requiredField, function (value) {
        if (this.parent.hasOwnProperty('description') && !value) {
          return false;
        }
        return true;
      })
      .notRequired(),
    explanation: explanationSchema.notRequired(),
  }),
  integer: yup.object().shape({
    text: yup.string().required(errors.requiredField),
    enableWhen: yup.array().of(enableWhenSchema).notRequired(),
    validationRule: numericValidationRuleSchema.notRequired(),
    sliderConfig: sliderConfigSchema.notRequired(),
    description: yup.mixed().test('description', errors.requiredField, function (value) {
      if (this.parent.hasOwnProperty('description') && !value) {
        return false;
      }
      return true;
    }),
    explanation: explanationSchema.notRequired(),
  }),
  decimal: yup.object().shape({
    text: yup.string().required(errors.requiredField),
    enableWhen: yup.array().of(enableWhenSchema).notRequired(),
    validationRule: numericValidationRuleSchema.notRequired(),
    description: yup.mixed().test('description', errors.requiredField, function (value) {
      if (this.parent.hasOwnProperty('description') && !value) {
        return false;
      }
      return true;
    }),
    explanation: explanationSchema.notRequired(),
  }),
  date: yup.object().shape({
    text: yup.string().required(errors.requiredField),
    enableWhen: yup.array().of(enableWhenSchema).notRequired(),
    description: yup.mixed().test('description', errors.requiredField, function (value) {
      if (this.parent.hasOwnProperty('description') && !value) {
        return false;
      }
      return true;
    }),
    explanation: explanationSchema.notRequired(),
  }),
  dateTime: yup.object().shape({
    text: yup.string().required(errors.requiredField),
    enableWhen: yup.array().of(enableWhenSchema).notRequired(),
    description: yup.mixed().test('description', errors.requiredField, function (value) {
      if (this.parent.hasOwnProperty('description') && !value) {
        return false;
      }
      return true;
    }),
    explanation: explanationSchema.notRequired(),
  }),
  // type group is for data grid question
  group: yup.object().shape({
    text: yup.string().required(errors.requiredField),
    enableWhen: yup.array().of(enableWhenSchema).notRequired(),
    description: yup.mixed().test('description', errors.requiredField, function (value) {
      if (this.parent.hasOwnProperty('description') && !value) {
        return false;
      }
      return true;
    }),
    explanation: explanationSchema.notRequired(),
    item: yup
      .array()
      .of(
        yup.object().shape({
          item: yup.array().of(gridColumnSchema).min(1, errors.requiredField).required(errors.requiredField),
        }),
      )
      .min(1, errors.requiredField)
      .required(errors.requiredField),
  }),
  choice: yup.object().shape({
    text: yup.string().required(errors.requiredField),
    description: yup.mixed().test('description', errors.requiredField, function (value) {
      if (this.parent.hasOwnProperty('description') && !value) {
        return false;
      }
      return true;
    }),
    explanation: explanationSchema.notRequired(),
    linearConfig: linearConfigSchema.notRequired(),
    answerOption: yup
      .array()
      .of(
        yup.object().shape({
          valueCoding: yup.object().shape({
            display: yup.string().required(errors.requiredField),
          }),
        }),
      )
      .min(1, errors.requiredField)
      .required(errors.requiredField),
    enableWhen: yup.array().of(enableWhenSchema).notRequired(),
  }),
};

const questionnaireDetailSchema = yup.object().shape({
  name: yup.string().required(errors.requiredField).min(2, errors.mustBeAtLeast2CharactersLong),
  title: yup.string().required(errors.requiredField).min(2, errors.mustBeAtLeast2CharactersLong),
});

export const validateQuestionnaireDetail = async (questionnaireDetail) => {
  try {
    await questionnaireDetailSchema.validate(questionnaireDetail, { abortEarly: false });
    return {}; // Return an empty object if validation succeeds
  } catch (error) {
    console.error(error);
    const errors = error.inner.reduce((errors, err) => {
      errors[err.path] = err.message;
      return errors;
    }, {});
    return errors; // Return an object with path and error message if validation fails
  }
};

export const validateQuestion = async (question) => {
  const schema = questionSchemas[question.type];
  try {
    await schema.validate(question, { abortEarly: false });
    return {}; // return empty object if validation succeeds
  } catch (error) {
    const errors = error?.inner?.reduce((errors, err) => {
      errors[err?.path] = err?.message;
      return errors;
    }, {});
    return errors; // return object with path and error message if validation fails
  }
};
