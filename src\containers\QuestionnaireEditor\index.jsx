'use client';
import React, { useState } from 'react';
import { Grid, Stack, Box } from '@mui/material';
import { SideMenu } from './components/SideMenu';
import { Editor } from './components/Editor';
import { editorScreens } from '../CommonConstants';
import { QuestionnaireControls } from './components/QuestionnaireControls';
import { QuestionnaireProvider } from '@/context/questionnairePages';
import { QuestionnaireDetailsProvider } from '@/context/questionnaireDetails';
import { QuestionnaireReportProvider, ValidationErrorProvider, ScoringProvider } from '@/context';
import { HeaderStyle } from '@/components';
import { TwoColumnPage } from '@/components';
import { DoublePanelBorder } from '@/components';

/**
 * @function QuestionnaireEditor
 * @returns {JSX.Element}
 */

/*
List of available props:
  codebookDefaultTemplate,
  htmlReportDefaultTemplate,
  handleNavigation,
  onSaveDraftCallback,
  onPublishCallback,
  onPreviewCallback,
  onDuplicateCallback,
  onDeleteCallback,
  existingQuestionnaireData,
  setExistingQuestionnaire,
  existingPdfTemplate,
  setExistingPdfTemplate,
*/

export const QuestionnaireEditor = (props) => {
  const { existingQuestionnaireData, existingPdfTemplate, codebookDefaultTemplate, htmlReportDefaultTemplate } = props;

  const [currentScreen, setCurrentScreen] = useState(editorScreens.PROPERTIES);

  const handleEditorScreenNavigation = (screen) => {
    setCurrentScreen(screen);
  };

  return (
    <>
      <ValidationErrorProvider>
        <QuestionnaireProvider existingQuestionnaireData={existingQuestionnaireData}>
          <QuestionnaireDetailsProvider existingQuestionnaireData={existingQuestionnaireData}>
            <ScoringProvider existingQuestionnaireData={existingQuestionnaireData}>
              <QuestionnaireReportProvider
                existingQuestionnaireData={existingQuestionnaireData}
                existingPdfTemplate={existingPdfTemplate}
                codebookDefaultTemplate={codebookDefaultTemplate}
                htmlReportDefaultTemplate={htmlReportDefaultTemplate}
              >
                <HeaderStyle>
                  <QuestionnaireControls {...props} />
                </HeaderStyle>
                <TwoColumnPage
                  leftColumn={
                    <DoublePanelBorder>
                      <SideMenu handleEditorScreenNavigation={handleEditorScreenNavigation} />
                    </DoublePanelBorder>
                  }
                  rightColumn={
                    <DoublePanelBorder>
                      <Editor
                        currentScreen={currentScreen}
                        codebookDefaultTemplate={codebookDefaultTemplate}
                        htmlReportDefaultTemplate={htmlReportDefaultTemplate}
                      />
                    </DoublePanelBorder>
                  }
                />
              </QuestionnaireReportProvider>
            </ScoringProvider>
          </QuestionnaireDetailsProvider>
        </QuestionnaireProvider>
      </ValidationErrorProvider>
    </>
  );
};
