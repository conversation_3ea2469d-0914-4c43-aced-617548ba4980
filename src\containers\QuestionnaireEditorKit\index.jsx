'use client';
import React, { useEffect, useMemo, useState } from 'react';
import { pages } from '../CommonConstants';
import { QuestionnaireList } from '../QuestionnaireList';
import { QuestionnaireEditor } from '../QuestionnaireEditor';
import { Box } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { ErrorOutline, InfoOutlined, TaskAlt, WarningAmberOutlined } from '@mui/icons-material';

const snackbarIconVariants = {
  success: <TaskAlt sx={{ fontSize: '20px' }} />,
  error: <ErrorOutline sx={{ fontSize: '20px' }} />,
  info: <InfoOutlined sx={{ fontSize: '20px' }} />,
  warning: <WarningAmberOutlined sx={{ fontSize: '20px' }} />,
};

export const QuestionnaireEditorKit = (props) => {
  const {
    questionnaireList, // Questionnaire list (in FHIR format)
    codebookDefaultTemplate = '', // Default template for codebook
    htmlReportDefaultTemplate = '', // Default template for html report
    onEditQuestionnaireCallback = () => {}, // Callback function to perform onClick of "Edit" button
    onSaveDraftCallback = () => {}, // Callback function to perform on onClick of "Save Draft" button
    onFinalizeCallback = () => {}, // Callback function to perform on onClick of "Finalize" button
    onPublishCallback = () => {}, // Callback function to perform on onClick of "Publish" button
    onDeleteCallback = () => {}, // Callback function to perform on onClick of "Delete" button
    onViewCallback = () => {}, // Callback function to perform on onClick of "View" button
    onPreviewCallback = () => {}, // Callback function to perform on onClick of "Preview" button
    onDownloadCodebookCallback = () => {}, // Callback function to perform on onClick of "Codebook" button
    onDuplicateCallback = () => {}, // Callback function to perform on onClick of "Duplicate" button
    onImportCallback = () => {}, // Callback function to perform on onClick of "Import" button
    onExportCallback = () => {}, // Callback function to perform on onClick of "Export" button
  } = props;

  const [currentPage, setCurrentPage] = useState(pages.questionnaireList);
  const [existingQuestionnaire, setExistingQuestionnaire] = useState(null);
  const [existingPdfTemplate, setExistingPdfTemplate] = useState(null);
  const [publishedRepository, setPublishRepository] = useState('');

  const handleNavigation = (page) => {
    setCurrentPage(page);
    if (page === pages.questionnaireList) {
      setExistingQuestionnaire(null);
      setExistingPdfTemplate(null);
    }
  };

  const handleEditQuestionnaire = async (questionnaireId, questionnaireStatus) => {
    setPublishRepository('no'); //only questionnaires that have not been finalized and have publishStatus "no" have edit option
    const response = await onEditQuestionnaireCallback(questionnaireId, questionnaireStatus);
    console.log({ response });
    if (response.success && response.questionnaire) {
      setExistingQuestionnaire(response.questionnaire);
      setExistingPdfTemplate(response.pdfTemplate);
      setCurrentPage(pages.questionnaireEditor);
    }
  };
  const handleViewQuestionnaire = async (questionnaireId, publishStatus) => {
    setPublishRepository(publishStatus);
    const response = await onViewCallback(questionnaireId, publishStatus);

    if (response.success && response.questionnaire) {
      setExistingQuestionnaire(response.questionnaire);
      setExistingPdfTemplate(response.pdfTemplate);
      setCurrentPage(pages.questionnaireEditor);
    }
  };

  const handleDuplicateQuestionnaire = (questionnaireId, publishStatus) => {
    onDuplicateCallback(questionnaireId, publishStatus);
  };

  const handleDeleteQuestionnaire = (questionnaireId, publishStatus) => {
    onDeleteCallback(questionnaireId, publishStatus);
  };

  const handleExportQuestionnaire = (questionnaireId, publishStatus) => {
    onExportCallback(questionnaireId, publishStatus);
  };

  const GetPage = useMemo(() => {
    if (currentPage === pages.questionnaireList) {
      return (
        <QuestionnaireList
          handleNavigation={handleNavigation}
          questionnaireList={questionnaireList}
          codebookDefaultTemplate={codebookDefaultTemplate}
          onImportCallback={onImportCallback}
          handlePublishQuestionnaire={onPublishCallback}
          handlePreviewQuestionnaire={onPreviewCallback}
          handleViewQuestionnaire={handleViewQuestionnaire}
          handleEditQuestionnaire={handleEditQuestionnaire}
          handleDownloadCodebook={onDownloadCodebookCallback}
          handleDuplicateQuestionnaire={handleDuplicateQuestionnaire}
          handleDeleteQuestionnaire={handleDeleteQuestionnaire}
          handleExportQuestionnaire={handleExportQuestionnaire}
        />
      );
    } else if (currentPage === pages.questionnaireEditor) {
      return (
        <QuestionnaireEditor
          publishedRepository={publishedRepository}
          htmlReportDefaultTemplate={htmlReportDefaultTemplate}
          codebookDefaultTemplate={codebookDefaultTemplate}
          handleNavigation={handleNavigation}
          onSaveDraftCallback={onSaveDraftCallback}
          onPublishCallback={onPublishCallback}
          onPreviewCallback={onPreviewCallback}
          handleDownloadCodebookCallback={onDownloadCodebookCallback}
          onDuplicateCallback={onDuplicateCallback}
          onDeleteCallback={onDeleteCallback}
          existingQuestionnaireData={existingQuestionnaire}
          setExistingQuestionnaire={setExistingQuestionnaire}
          existingPdfTemplate={existingPdfTemplate}
          setExistingPdfTemplate={setExistingPdfTemplate}
          onFinalizeCallback={onFinalizeCallback}
          onExportCallback={handleExportQuestionnaire}
        />
      );
    }
  }, [currentPage, questionnaireList, existingQuestionnaire]);

  return (
    <>
      <SnackbarProvider iconVariant={snackbarIconVariants} maxSnack={3}>
        <Box>{GetPage}</Box>
      </SnackbarProvider>
    </>
  );
};
