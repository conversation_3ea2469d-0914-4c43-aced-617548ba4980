import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>ack } from '@mui/material';
import { Add } from '@mui/icons-material';
import { MultipleResponseOptions } from './components/MultipleResponseOptions';
import { characterLength } from '@/containers/CommonConstants';
import { QuestionTextInput } from './components/QuestionTextInput';

export const Dropdown = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;

  const [questionText, setQuestionText] = useState(question?.text || '');
  const [answerOption, setAnswerOptions] = useState(
    question?.answerOption?.length
      ? question.answerOption
      : [
          {
            valueCoding: {
              id: 0,
              sequence: 1,
              display: '',
              code: 1,
              extension: [
                {
                  url: 'Item/AnswerOption/ValueCoding/sequence-value',
                  valueInteger: 1,
                },
              ],
            },
          },
        ],
  );

  const handleQuestionCreation = (question) => {
    handleCreateQuestion(pageIndex, questionIndex, { ...question, type: 'choice' });
  };

  const handleQuestionTextChange = (event) => {
    // setQuestionText(event.target.value);
    const newText = event.target.value.slice(0, characterLength.itemTextLength);
    setQuestionText(newText);
    let newQuestion = {
      ...question,
      text: event.target.value,
    };
    handleQuestionCreation(newQuestion);
  };

  const handleAddOption = () => {
    let options = [...answerOption];
    let newOption = {
      valueCoding: {
        id: options?.length ? options[options.length - 1]?.valueCoding.id + 1 : 0,
        sequence: options?.length ? options[options.length - 1]?.valueCoding.sequence + 1 : 1,
        display: '',
        code: options?.length ? options[options.length - 1]?.valueCoding?.code + 1 : 1,
        extension: [
          {
            url: 'Item/AnswerOption/ValueCoding/sequence-value',
            valueInteger: options?.length ? options[options.length - 1]?.valueCoding.sequence + 1 : 1,
          },
        ],
      },
    };
    options.push(newOption);
    setAnswerOptions(options);
  };

  const handleAnswerOptionChange = (keyName, keyValue, index) => {
    let options = structuredClone(answerOption);
    options[index].valueCoding[keyName] = keyValue;
    setAnswerOptions(options);

    const newQuestion = structuredClone(question);
    newQuestion.answerOption = options;

    handleQuestionCreation(newQuestion);
  };

  const handleRemoveAnswerOption = (optionIndex) => {
    let options = structuredClone(answerOption);
    options.splice(optionIndex, 1);
    setAnswerOptions(options);

    const newQuestion = structuredClone(question);
    newQuestion.answerOption = options;

    handleQuestionCreation(newQuestion);
  };

  return (
    <>
      <Stack>
        <QuestionTextInput
          value={questionText}
          onChange={handleQuestionTextChange}
          question={question}
          characterLimit={characterLength.itemTextLength}
        />
        {DescriptionComponent}
        <MultipleResponseOptions
          type="dropdown"
          answerOption={answerOption}
          handleAnswerOptionCallback={handleAnswerOptionChange}
          handleRemoveAnswerOption={handleRemoveAnswerOption}
          question={question}
        />

        <Stack direction="row" sx={{ mt: 2 }}>
          <Button
            onClick={handleAddOption}
            startIcon={<Add fontSize="small" />}
            sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
          >
            Add Option
          </Button>
        </Stack>
      </Stack>
    </>
  );
};
