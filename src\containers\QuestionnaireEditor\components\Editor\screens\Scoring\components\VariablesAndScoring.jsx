import React, { useContext } from 'react';
import { Typography, Paper, Grid, Button } from '@mui/material';
import { CambianTooltip } from '@/components';
import { errors, strings } from '@/utility/strings';
import AddIcon from '@mui/icons-material/Add';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { styled } from '@mui/system';
import { removeHtmlTags } from '@/utility/utils';
import { useDrag } from 'react-dnd';
import useNotification from '@/hooks/useNotification';
import { QuestionnaireContext } from '@/context';
import { SELECT_VARIABLE } from '@/context/scoring';

export const VariablesAndScoring = (props) => {
  const { variables, handleAddVariable, dispatchScoringAction, handleCheckForDuplicates } = props;
  const { questionnaireState: questionnairePages, dispatchQuestionnaireAction } = useContext(QuestionnaireContext);
  const openSnackbar = useNotification();

  const paperStyles = {
    mt: 2,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    flexWrap: 'nowrap',
  };

  const addVariableStyle = {
    padding: '8px 25px',
    cursor: 'pointer',
    my: 1,
    fontSize: '14px',
    width: '100%',
    justifyContent: 'flex-start',
    '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
  };

  const handleSelectVariableDispatch = (obj, keyVal) => {
    const duplicates = handleCheckForDuplicates(variables);

    if (duplicates) {
      dispatchScoringAction({ type: SELECT_VARIABLE, key: keyVal });
    } else {
      openSnackbar({ variant: 'error', msg: errors.fillRequiredFields });
    }
  };

  return (
    <Grid item xs={3}>
      <Paper sx={paperStyles}>
        <Grid container direction="column" justifyContent="flex-start">
          <Grid item sx={{ paddingTop: 2 }}>
            <Typography variant="h3" sx={{ padding: '5px 20px' }}>
              {strings.variableTitle}
            </Typography>
          </Grid>
          <Grid item sx={{ marginTop: 1, width: '100%' }}>
            {variables.map((obj, key) => (
              <DraggableVariableButton
                key={key}
                keyVal={key}
                handleVariableClick={() => handleSelectVariableDispatch(obj, key)}
                selected={obj.selected}
                text={obj.name}
              />
            ))}
          </Grid>
          <Grid item>
            <Button
              onClick={() => handleAddVariable()}
              startIcon={<AddIcon width="1" />}
              color="primary"
              sx={addVariableStyle}
            >
              {strings.addVariable}
            </Button>
          </Grid>
          <Grid item sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
            <CambianTooltip
              size="200"
              title={
                <div>
                  <Typography sx={{ fontSize: '12px' }}>{strings.descriptionTooltip}</Typography>
                </div>
              }
            >
              <Typography variant="h3" sx={{ padding: '5px 10px 5px 20px' }}>
                {strings.itemScores}
              </Typography>
            </CambianTooltip>
          </Grid>
          <Grid sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }} item>
            {questionnairePages.map((itemArr) => {
              return itemArr.item.map((item, key) => {
                return <DraggableText text={item.linkId} key={key} description={removeHtmlTags(item.text)} />;
              });
            })}
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  );
};

const DraggableText = ({ text, description }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'TEXT',
    item: { text },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  return (
    <div style={{ width: '100%' }} ref={drag}>
      <CambianTooltip title={description} sx={{ m: 1 }} placement="right">
        <label
          style={{
            opacity: isDragging ? 0.5 : 1,
            cursor: 'grab',
            marginBottom: 1,
            paddingLeft: 20,
          }}
        >
          {isDragging ? `#{${text}}` : text}
        </label>
      </CambianTooltip>
    </div>
  );
};

const VariableButton = styled(Button)(({ selected }) => ({
  minWidth: '90%',
  backgroundColor: selected ? '#4D76A9' : '#F3F3F3',
  color: selected ? 'white' : '#000000',
  border: 'none',
  cursor: 'pointer',
  justifyContent: 'flex-start',
  height: '28px',
  textAlign: 'left',
  '&:hover': {
    backgroundColor: selected ? '#4D76A9' : '#F3F3F3', // Change the color on hover
    color: selected ? 'white' : 'black', // Change the text color on hover
  },
}));

const VariableButtonDiv = styled('div')(({ selected }) => ({
  backgroundColor: selected ? '#4D76A9' : '#F3F3F3',
  color: selected ? 'white' : '#000000',
  paddingLeft: '18px',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-start',
  '&:hover': {
    backgroundColor: selected ? '#4D76A9' : '#F3F3F3', // Change the color on hover
    color: selected ? 'white' : 'black', // Change the text color on hover
  },
}));

const DraggableVariableButton = ({ keyVal, handleVariableClick, selected, text }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'TEXT',
    item: { type: 'TEXT', text },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  return (
    <VariableButtonDiv key={keyVal} selected={selected}>
      <VariableButton selected={selected} onClick={() => handleVariableClick(keyVal)}>
        {text}
      </VariableButton>
      <div style={{ width: '10%', display: 'flex' }} ref={drag}>
        <DragIndicatorIcon sx={{ color: 'rgb(173, 173, 173)', cursor: 'move' }} />
      </div>
    </VariableButtonDiv>
  );
};
