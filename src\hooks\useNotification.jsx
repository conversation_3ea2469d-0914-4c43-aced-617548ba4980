import React, { Fragment, useEffect, useState } from 'react';
import { Typography, IconButton } from '@mui/material';
import { Clear } from '@mui/icons-material';
import { useSnackbar } from 'notistack';

const useNotification = () => {
  const [conf, setConf] = useState({});
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const action = (key) => (
    <Fragment style={{ backgroundColor: '#4D76A9' }}>
      <IconButton
        onClick={() => {
          closeSnackbar(key);
        }}
      >
        <Clear fontSize="small" sx={{ color: '#fff' }} />
      </IconButton>
    </Fragment>
  );

  useEffect(() => {
    if (conf?.msg) {
      let variant = 'info';
      if (conf.variant) {
        variant = conf.variant;
      }
      enqueueSnackbar(
        <Typography variant="body2" sx={{ pl: 1 }}>
          {conf.msg}
        </Typography>,
        {
          variant: variant,
          autoHideDuration: 3000,
          action,
        },
      );
    }
  }, [conf]);

  return setConf;
};

export default useNotification;
