{"name": "@appscoopsolutions/cambian-questionnaire-editor", "version": "0.1.77", "publishConfig": {"registry": "https://npm.pkg.github.com/appscoopsolutions"}, "engines": {"node": ">=20.0.0", "npm": "please use YARN", "yarn": ">= 1.22.18"}, "scripts": {"rollup": "rollup -c", "dev": "next dev -p 5005", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint --fix", "format": "prettier --write \"./src/**/*.{js,jsx,ts,tsx,css,}\" --config ./.prettierrc", "yalc-publish": "yalc publish"}, "dependencies": {"short-uuid": "^4.2.2", "uuid": "^8.3.2", "yup": "^1.3.3"}, "peerDependencies": {"@date-io/date-fns": "^3.0.0", "@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@mui/icons-material": "^5.2.5", "@mui/lab": "^5.0.0-alpha.66", "@mui/material": "^5.15.1", "date-fns": "^3.2.0", "notistack": "^2.0.3", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0"}, "devDependencies": {"@appscoopsolutions/component-ui": "^0.0.68", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-react": "^7.23.3", "@date-io/date-fns": "^3.0.0", "@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@mui/icons-material": "^5.2.5", "@mui/lab": "^5.0.0-alpha.66", "@mui/material": "^5.15.1", "@mui/x-data-grid": "^8.6.0", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-url": "^8.0.2", "@types/scheduler": "^0.16.8", "date-fns": "^3.2.0", "eslint": "^7.32.0 || ^8.2.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "husky": "^8.0.3", "lint-staged": "^12.3.4", "notistack": "^2.0.3", "prettier": "3.1.0", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "rollup": "^2.79.1", "rollup-plugin-postcss": "^4.0.2"}, "lint-staged": {"/src/**/*.{js,jsx}": ["yarn lint --fix", "yarn format"]}, "husky": {"hooks": {"pre-commit": "lint-staged --staged"}}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"]}