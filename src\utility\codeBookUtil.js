import { addKeysToQuestionnaireItem, extractExtension } from './utils.js';

export const headerTemplate = `
<h2 style="text-align: center;"><strong>General Data Format</strong></h2>
<p>The data export file for questionnaire responses has the following characteristics:</p>
<ol>
<li data-list-text="1.">
<p>The file format is &ldquo;csv&rdquo;(machine readable)</p>
</li>
<li data-list-text="2.">
<p>One row for each questionnaire response (Long Format)</p>
</li>
<li data-list-text="3.">
<p>Each row contains the following:</p>
<ol style="list-style-type: lower-alpha;">
<li data-list-text="a.">
<p>An identifier for the questionnaire</p>
</li>
<li data-list-text="b.">
<p>The group id of the group the participant is a member of</p>
</li>
<li data-list-text="c.">
<p>The participant id of the user filling in the response</p>
</li>
<li data-list-text="d.">
<p>The start date (YYYY-MM-DD hh:mm:ss aa) when the participant begins to work on the response</p>
</li>
<li data-list-text="e.">
<p>The completion date (YYYY-MM-DD hh:mm:ss aa)</p>
</li>
<li data-list-text="f.">
<p>Time spent in completing questionnaire (in seconds)</p>
</li>
<li data-list-text="g.">
<p>The response for each item (Note: For multiple choice items that allow multiple responses, each allowable response is turned into a item with the possible response of Yes or No. Free form text responses are put in double quotes (e.g.,”I experienced some pain”) so commas can be used inside the response value. Double quotes are escaped with another double quote (e.g.,”I experienced some “”phantom”” pain”). A skipped item will have the corresponding item response set to -99.)</p>
</li>
<li data-list-text="h.">
<p>The computed scores if applicable (Note: if a score cannot be computed because of missing data, the corresponding field will be set to -99)</p>
</li>
</ol>
</li>
</ol>`;

export const questionnaireDetailsTemplate = `<h1 style="text-align: center;">{Questionnaire.title}</h1>
<h3>Questionnaire and Item mapping</h3>
<p>Each questionnaire is given a unique numerical identifier and each item within a questionnaire is given a name that is used to define the columns used in the data export file.</p>
<table style="border-collapse: collapse; width: 900px;">
<tbody>
<tr>
<td style="width: 714px; border-style: solid; padding-left: 10px;">
<p>{Questionnaire.title}</p>
</td>
<td style="width: 178px; border-style: solid; padding-left: 10px;">
<p>{Questionnaire.id}</p>
</td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
{Questionnaire.mappedQuestionsList}
<p>&nbsp;</p>
<p>Each computed score within a questionnaire is given a name that is used to define the columns used in the data export file.</p>
<table style="border-collapse: collapse; width: 900.359px;">
<tbody>
<tr>
<td style="width: 714px; border-style: solid; padding-left: 10px;">
<p>score</p>
</td>
<td style="width: 178px; border-style: solid; padding-left: 10px;">
<p>S1</p>
</td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
<p>With the information above, columns that will be presented in an entry for {Questionnaire.title} are defined as:</p>
<p>{Questionnaire.questionnaireColumns}</p>
<p>&nbsp;</p>
<h3>Item response mapping</h3>
<p>Allowed responses for each item are shown below:</p>
<p>&nbsp;</p>
{Questionnaire.mappedResponseList}
<p>&nbsp;</p>
<h3>Sample Data</h3>
<p>k45e7b06-1295-47f2-9577-d8e4d43c5333,Item1, Item2, Item3, S1</p>
<p>m5187b06-8321-88i2-2342-h456w234l231,Item1, Item2, Item3, Item4, Item5, Item6, S1</p>
`;

export const codebookTemplateData = `<h1 style="color: rgb(77, 118, 169); text-align: center;">Codebook</h1>
<p><br>The Codebook consists of 2 parts -&nbsp;</p>
<ul>
    <li>&nbsp;A static informative part that has the General Data Format is also given. It remains the same for codebooks for all questionnaires.</li>
    <li>A dynamic part that maps over the questionnaire data and provides information about the specific questionnaire like the list of items and their avilable responses. There are certain static labels and texts in this part as well. The dynamic variables that are to be replaced are present in curly brackets.<br><br></li>
</ul>
<h1 style="color: #4D76A9;">Codebook Variables&nbsp;</h1>
<p style="font-weight: 400;">Variables are used to display dynamic values inside the Codebook template. It is mandatory to use the variables inside curly brackets. For example -</p>
<p style="font-weight: 400;">{Questionnaire.title} -&gt; EQ-5D-5L</p>
<p style="font-weight: 400;">Following are the variables used in the Codebook Template:</p>
<ul>
    <li style="font-weight: 400;"><strong>Questionnaire.title</strong> &ndash; replaced by Questionnaire Title.</li>
    <li><strong>Questionnaire.id</strong><strong>&nbsp;-&nbsp;</strong>replaced by the Questionnaire id that is generated upon saving the questionnaire.</li>
    <li><strong>Questionnaire.mappedQuestionsList -&nbsp;</strong>replaced by all the item&apos;s list in a tabular form showing the item text in first column and Q&lt;serialNumber&gt; in the second column.</li>
    <li><strong>Questionnaire.questionnaireColumns -&nbsp;</strong>replaced by a list of columns that will be present for that entry. It will be in the format - &lt;questionnaireId&gt;, &lt;Q&lt;serialNumber&gt;&gt;, S1</li>
    <li><strong>Questionnaire.mappedResponseList -&nbsp;</strong>It will be replaced by a list of response type for each item. If the item is of &apos;choice&apos; type, all options will be displayed in the first column and serial number in the second column.</li>
</ul>`;

export const generateCodebookData = (existingQuestionnaireData, questionnaireState, questionnaireDetails) => {
  const syntaxesToReplace = [
    '{Questionnaire.title}',
    '{Questionnaire.id}',
    '{Questionnaire.mappedQuestionsList}',
    '{Questionnaire.questionnaireColumns}',
    '{Questionnaire.mappedResponseList}',
  ];

  // ---------------
  // This portion of code is required for coordinator. Coordinator separates list and edit page. The list page does not pass questionnaireState and questionnaireDetails.
  if (!questionnaireState) {
    questionnaireState = addKeysToQuestionnaireItem(structuredClone(existingQuestionnaireData?.item));
  }
  if (!questionnaireDetails) {
    questionnaireDetails = existingQuestionnaireData;
  }
  // ---------------

  const codeBookEditorTemplate =
    extractExtension(existingQuestionnaireData?.extension, 'codeBookHtmlData')?.valueString || '';

  const mappedQuestions = () => {
    if (questionnaireState[0]?.item?.length) {
      let serialNumber = 1;
      const tableElement = document.createElement('table');
      tableElement.style.cssText = 'border-collapse: collapse; width: 900px;';
      const tbodyElement = document.createElement('tbody');
      tableElement.appendChild(tbodyElement);

      questionnaireState.forEach((questionnaireItem, stateIndex) => {
        questionnaireItem.item.forEach((item, itemIndex) => {
          const trElement = document.createElement('tr');
          const td1Element = document.createElement('td');
          td1Element.style.cssText = 'width: 714px; border-style: solid; padding-left: 10px;';
          td1Element.innerHTML = `<p>${item.text}</p>`;
          trElement.appendChild(td1Element);
          const td2Element = document.createElement('td');
          td2Element.style.cssText = 'width: 178px; border-style: solid; padding-left: 10px;';
          td2Element.innerHTML = `<p>Item${serialNumber}</p>`;
          serialNumber++;
          trElement.appendChild(td2Element);
          tbodyElement.appendChild(trElement);
        });
      });

      return tableElement.outerHTML;
    }
    return '{Questionnaire.mappedQuestionsList}';
  };

  const generateColumnNames = () => {
    if (questionnaireState[0]?.item?.length) {
      let serialNumber = 1;
      const questionNumbers = questionnaireState
        .flatMap((page) => page.item.map((_, index) => `Item${serialNumber++}`))
        .join(', ');
      return `${questionnaireDetails?.id || '{Questionnaire.id}'}, ${questionNumbers}, S1`;
    }
    return '{Questionnaire.questionnaireColumns}';
  };

  const mappedResponseOptions = () => {
    if (questionnaireState[0]?.item?.length) {
      let serialNumber = 1;
      const containerElement = document.createElement('div');

      questionnaireState.forEach((questionnaireItem, stateIndex) => {
        questionnaireItem.item.forEach((item, itemIndex) => {
          const questionHeading = document.createElement('p');
          questionHeading.style.cssText = 'font-weight: bold; margin-top: 10px;';
          questionHeading.textContent = `Responses for Item${serialNumber}:`;
          serialNumber++;
          containerElement.appendChild(questionHeading);

          const questionTable = document.createElement('table');
          questionTable.style.cssText = 'border-collapse: collapse; width: 900px;';
          const tbodyElement = document.createElement('tbody');
          questionTable.appendChild(tbodyElement);

          if (item.type === 'choice' && item.answerOption && item.answerOption.length > 0) {
            const answerOptions = item.answerOption;

            answerOptions.forEach((option, optionIndex) => {
              const trElement = document.createElement('tr');
              const td1Element = document.createElement('td');
              td1Element.style.cssText = 'width: 714px; border-style: solid; padding-left: 10px;';
              td1Element.innerHTML = `<p>${option.valueCoding.display}</p>`;
              trElement.appendChild(td1Element);

              const td2Element = document.createElement('td');
              td2Element.style.cssText = 'width: 178px; border-style: solid; padding-left: 10px;';
              td2Element.innerHTML = `<p>${optionIndex + 1}</p>`;
              trElement.appendChild(td2Element);

              tbodyElement.appendChild(trElement);
            });
          } else {
            const trElement = document.createElement('tr');
            const tdElement = document.createElement('td');
            tdElement.style.cssText = 'width: 892px; border-style: solid; padding-left: 10px;';
            tdElement.innerHTML = `<p>${item.type}</p>`;
            trElement.appendChild(tdElement);

            tbodyElement.appendChild(trElement);
          }

          questionTable.appendChild(tbodyElement);
          containerElement.appendChild(questionTable);
        });
      });

      return containerElement.outerHTML;
    }

    return '{Questionnaire.mappedResponseList}';
  };

  const replaceHtmlTemplateWithValues = () => {
    let replacedHtml = codeBookEditorTemplate;
    syntaxesToReplace.forEach((syntax) => {
      const key = syntax.replace(/({Questionnaire\.|})/g, '');
      const value = questionnaireDetails[key];
      if (value !== undefined && value !== null && value !== '') {
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), value);
      } else if (syntax === '{Questionnaire.mappedQuestionsList}') {
        const mappedQuestionsResult = mappedQuestions();
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), mappedQuestionsResult || '');
      } else if (syntax === '{Questionnaire.questionnaireColumns}') {
        const columnNames = generateColumnNames();
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), columnNames || '');
      } else if (syntax === '{Questionnaire.mappedResponseList}') {
        const mappedResponses = mappedResponseOptions();
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), mappedResponses || '');
      }
    });
    return replacedHtml;
  };

  const codeBookFinalHtml = replaceHtmlTemplateWithValues();
  return codeBookFinalHtml;
};
