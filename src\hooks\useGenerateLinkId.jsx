import React, { useRef } from 'react';

export const useGenerateLinkId = () => {
  const lastQuestionLinkId = useRef(null);
  const lastPageLinkId = useRef(null);

  const generateItemLinkId = (questionnaire, startId = null) => {
    if (startId !== null) {
      lastQuestionLinkId.current = startId;
    }

    if (lastQuestionLinkId.current) {
      const newLinkId = lastQuestionLinkId.current + 1;
      lastQuestionLinkId.current = newLinkId;
      return `Item${newLinkId}`;
    }

    const lastPageItem = questionnaire[questionnaire.length - 1]?.item;
    const lastItemLinkId = lastPageItem[lastPageItem.length - 1]?.linkId;
    if (!lastItemLinkId) {
      return `Item${2}`;
    }

    const nextItemLinkId = Number(lastItemLinkId.replace('Item', '')) + 1;

    lastQuestionLinkId.current = nextItemLinkId;
    return `Item${nextItemLinkId}`;
  };

  const generatePageLinkId = (questionnairePages) => {
    if (lastPageLinkId.current) {
      const nextPageLinkId = lastPageLinkId.current + 1;
      lastPageLinkId.current = nextPageLinkId;
      return `Group${nextPageLinkId}`;
    }

    let nextPageLinkId = questionnairePages ? questionnairePages.length + 1 : 1;
    lastPageLinkId.current = nextPageLinkId;

    return `Group${nextPageLinkId}`;
  };

  const updateLastQuestionLinkId = (newLastLinkId) => {
    lastQuestionLinkId.current = newLastLinkId;
  };

  return {
    generateItemLinkId,
    generatePageLinkId,
    updateLastQuestionLinkId,
  };
};
