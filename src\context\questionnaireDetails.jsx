import React, { createContext, useEffect, useState } from 'react';
import { CANNOT_LOCATE_STRING } from '@/containers/CommonConstants';

// Create the context
export const QuestionnaireDetailsContext = createContext();

// Create a provider component
export const QuestionnaireDetailsProvider = ({ children, existingQuestionnaireData }) => {
  // Define the initial state of your context
  const {
    name = '',
    title = '',
    description = '',
    status = '',
    id = '',
    subjectType = 'Patient',
  } = existingQuestionnaireData || {};
  const [initialState, setInitialState] = useState({
    status,
    name,
    title,
    id,
    description:
      existingQuestionnaireData?.description && existingQuestionnaireData?.description !== CANNOT_LOCATE_STRING
        ? existingQuestionnaireData?.description
        : '',
    subjectType,
  });
  const [questionnaireDetails, setQuestionnaireDetails] = useState(initialState);

  useEffect(() => {
    if (
      existingQuestionnaireData?.name ||
      existingQuestionnaireData?.id ||
      existingQuestionnaireData?.title ||
      existingQuestionnaireData?.description ||
      existingQuestionnaireData?.status ||
      existingQuestionnaireData?.subjectType
    ) {
      setInitialState({
        status,
        name,
        id,
        title,
        description:
          existingQuestionnaireData?.description && existingQuestionnaireData?.description !== CANNOT_LOCATE_STRING
            ? existingQuestionnaireData?.description
            : '',
        subjectType,
      });
    }
  }, [name, title, description, status, id, subjectType]);

  const handleQuestionnaireDetailsChange = (updatedDetail) => {
    setQuestionnaireDetails((prevValue) => ({ ...prevValue, ...updatedDetail }));
  };

  return (
    <QuestionnaireDetailsContext.Provider
      value={{ questionnaireDetails, handleQuestionnaireDetailsChange, initialState }}
    >
      {children}
    </QuestionnaireDetailsContext.Provider>
  );
};
