import React, { useState, useContext } from 'react';
import { Box, Paper, TextField, Typography, Modal, Stack, IconButton } from '@mui/material';
import { Code, Help } from '@mui/icons-material';
import { modes } from '@/containers/CommonConstants';
import { ShowTemplateReportFile } from './ShowTemplateReportFile';
import { CambianTooltip } from '@/components';
import { QuestionnaireReportContext } from '@/context';
import { strings } from '@/utility/strings';
import { QuestionnaireDetailsContext } from '@/context';
import { useDebouncing } from '@/hooks/useDebouncing';

export const ReportHTMLTemplate = ({ htmlReportDefaultTemplate }) => {
  const { questionnaireReport, handleQuestionnaireReportContext } = useContext(QuestionnaireReportContext);
  const { debounceFunction } = useDebouncing();
  const { questionnaireDetails } = useContext(QuestionnaireDetailsContext);
  const [htmlReport, setHtmlReport] = useState(questionnaireReport.reportHtml || htmlReportDefaultTemplate);
  const [mode, setMode] = useState('Editor');
  const [openTemplateReportFile, setOpenTemplateReportFile] = useState(false);

  const syntaxesToReplace = ['{Questionnaire.title}', '{Questionnaire.description}'];

  const replaceHtmlTemplateWithValues = () => {
    let replacedHtml = htmlReport;
    syntaxesToReplace.forEach((syntax) => {
      const key = syntax.replace(/({Questionnaire\.|})/g, '');
      const value = questionnaireDetails[key];
      if (value !== undefined && value !== null && value !== '') {
        replacedHtml = replacedHtml.replace(new RegExp(syntax, 'g'), value);
      }
    });
    return replacedHtml;
  };

  // const handleBlur = (event) => {
  //   event.preventDefault();
  //   setHtmlReport(event.target.value);
  //   debounceFunction(() => handleQuestionnaireReportContext({ reportHtml: event.target.value }), 300);
  // };
  const ModalStyle = {
    margin: 'auto',
    marginTop: '20px',
    maxWidth: '80%',
    borderRadius: '10px',
    maxHeight: '80vh',
    overflowY: 'scroll',
    // '&::-webkit-scrollbar': { display: 'none' },
  };
  const onClose = () => {
    setOpenTemplateReportFile(false);
  };

  const handleChange = (event) => {
    event.preventDefault();
    setHtmlReport(event.target.value);
    debounceFunction(() => handleQuestionnaireReportContext({ reportHtml: event.target.value }), 300);
  };

  return (
    <>
      <Paper sx={{ border: 'none' }}>
        <Stack direction="row" justifyContent="space-between">
          <Stack direction="flex-start" alignItems="center">
            <Typography variant="h3">{strings.htmlReportTemplate}</Typography>
            <CambianTooltip title={strings.reportSyntaxHelpDocument}>
              <IconButton color="primary" onClick={() => setOpenTemplateReportFile(true)}>
                <Help
                  sx={{
                    fontSize: '20px',
                    fontWeight: 'normal',
                    color: '#0000008A',
                  }}
                />
              </IconButton>
            </CambianTooltip>
          </Stack>
          <CambianTooltip title={mode === modes.editor ? strings.switchToPreview : strings.switchToEditor}>
            <IconButton color="primary" onClick={() => setMode(mode === modes.editor ? modes.preview : modes.editor)}>
              <Code
                fontSize="large"
                sx={{
                  background: mode === modes.editor ? '#4d76a933' : 'disabled',
                  border: '1px solid #4D76A9',
                  borderRadius: '4px',
                  height: '2rem',
                  width: '3rem',
                }}
              />
            </IconButton>
          </CambianTooltip>
        </Stack>
      </Paper>
      <Box>
        {mode === modes.editor ? (
          <Paper sx={{ mt: 2, border: 'none' }}>
            <TextField
              sx={{
                maxWidth: '100%',
                '& .MuiOutlinedInput-root': {
                  maxWidth: '100% ',
                },
              }}
              id="outlined-multiline-flexible"
              defaultValue={htmlReport}
              multiline
              rows={22}
              // onBlur={(event) => handleBlur(event)}
              onChange={(event) => handleChange(event)}
            />
          </Paper>
        ) : (
          <Paper sx={{ marginTop: 2, padding: 2, minHeight: 483 }}>
            <Typography dangerouslySetInnerHTML={{ __html: `${replaceHtmlTemplateWithValues()}` }}></Typography>
          </Paper>
        )}
      </Box>
      <Modal open={openTemplateReportFile} onClose={() => setOpenTemplateReportFile(false)}>
        <Box sx={ModalStyle}>
          <ShowTemplateReportFile onClose={onClose} />
        </Box>
      </Modal>
    </>
  );
};
