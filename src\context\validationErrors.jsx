import React, { createContext, useState } from 'react';

export const ValidationErrorContext = createContext();

export const ValidationErrorProvider = ({ children }) => {
  const [questionErrors, setQuestionErrors] = useState({});
  const [questionnaireDetailErrors, setQuestionnaireDetailErrors] = useState({});
  const [scoringErrors, setScoringErrors] = useState({ missingFields: false });

  const handleQuestionValidationErrors = (newErrors) => {
    setQuestionErrors(newErrors);
  };

  const handleQuestionnaireDetailValidationErrors = (newErrors) => {
    setQuestionnaireDetailErrors(newErrors);
  };

  const handleScoringValidationErrors = (missingFields) => {
    setScoringErrors({ missingFields: missingFields });
  };

  return (
    <ValidationErrorContext.Provider
      value={{
        questionErrors,
        handleQuestionValidationErrors,
        questionnaireDetailErrors,
        handleQuestionnaireDetailValidationErrors,
        scoringErrors,
        handleScoringValidationErrors,
      }}
    >
      {children}
    </ValidationErrorContext.Provider>
  );
};
