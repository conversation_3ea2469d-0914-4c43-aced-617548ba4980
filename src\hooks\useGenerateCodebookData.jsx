import { useContext } from 'react';
import { QuestionnaireContext, QuestionnaireDetailsContext } from '@/context';
import { generateCodebookData } from '@/utility/codeBookUtil';

export const useGenerateCodebookData = (existingQuestionnaireData) => {
  const { questionnaireState } = useContext(QuestionnaireContext);
  const { questionnaireDetails } = useContext(QuestionnaireDetailsContext);

  const codeBookHtml = generateCodebookData(existingQuestionnaireData, questionnaireState, questionnaireDetails);
  return codeBookHtml;
};
