import React, { useState } from 'react';
import { Stack } from '@mui/material';
import { QuestionTextInput } from './components/QuestionTextInput';
import { ResponseValidation } from './components/ResponseValidation';
import { characterLength } from '@/containers/CommonConstants';
import { mergeTwoExtension } from '@/utility/utils';

export const Number = (props) => {
  const { pageIndex, questionIndex, handleCreateQuestion, question, DescriptionComponent } = props;
  const [questionText, setQuestionText] = useState(question?.text || '');

  const extension = [
    {
      url: 'Item/description',
      valueString: null,
    },
    {
      url: 'Item/explanation',
      valueString: null,
    },
    {
      url: 'Item/trendable',
      valueBoolean: false,
    },
    {
      url: 'Item/horizontal-orientation',
      valueBoolean: false,
    },
    {
      url: 'Item/hide-question',
      valueBoolean: false,
    },
    {
      url: 'Item/question-type-id',
      valueInteger: 5513,
    },
  ];

  const handleQuestionTextChange = (event) => {
    // setQuestionText(event.target.value);
    const newText = event.target.value.slice(0, characterLength.itemTextLength);
    setQuestionText(newText);

    let newQuestion = structuredClone(question);
    newQuestion = {
      ...newQuestion,
      type: 'decimal',
      text: event.target.value,
      extension: extension,
    };
    handleCreateQuestion(pageIndex, questionIndex, newQuestion);
  };

  const handleCreateValidationRule = (ruleExtension, validationRule) => {
    const EXTENSION_BASE_URL = 'Item';
    const possibleExtensionUrls = [
      `${EXTENSION_BASE_URL}/min-value`,
      `${EXTENSION_BASE_URL}/max-value`,
      `${EXTENSION_BASE_URL}/min-exclusion`,
      `${EXTENSION_BASE_URL}/max-exclusion`,
    ];
    let newQuestion = structuredClone(question);
    // newQuestion.extension = mergeTwoExtension(newQuestion.extension, ruleExtension);
    const filteredExtension = newQuestion.extension?.filter((item) => !possibleExtensionUrls.includes(item.url));
    newQuestion.extension = [...filteredExtension, ...ruleExtension];
    if (Object.keys(validationRule)?.length) {
      newQuestion.validationRule = validationRule;
    } else {
      newQuestion.validationRule && delete newQuestion.validationRule;
    }

    handleCreateQuestion(pageIndex, questionIndex, newQuestion);
  };

  return (
    <Stack>
      <QuestionTextInput
        value={questionText}
        onChange={handleQuestionTextChange}
        question={question}
        characterLimit={characterLength.itemTextLength}
      />
      {DescriptionComponent}
      <ResponseValidation
        question={question}
        questionExtension={question.extension}
        handleCreateValidationRule={handleCreateValidationRule}
      />
    </Stack>
  );
};
