import React, { useContext, useEffect, useMemo, useState } from 'react';
import {
  FormHelperText,
  Stack,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Typography,
  Grid,
  Box,
  FormControl,
} from '@mui/material';
import { Close } from '@mui/icons-material';
import { characterLength } from '@/containers/CommonConstants';
import { placeholders, strings } from '@/utility/strings';
import { ValidationErrorContext } from '@/context';

const EXTENSION_BASE_URL = 'Item';

const responseValidationOptions = [
  {
    id: 0,
    symbol: '>',
    label: 'Greater than',
  },
  {
    id: 1,
    symbol: '>=',
    label: 'Greater than or equal to',
  },
  {
    id: 2,
    symbol: '<',
    label: 'Less than',
  },
  {
    id: 3,
    symbol: '<=',
    label: 'Less than or equal to',
  },
  {
    id: 4,
    symbol: '<<',
    label: 'Between',
  },
];

export const ResponseValidation = (props) => {
  const { question, handleCreateValidationRule, questionExtension } = props;
  const { questionErrors } = useContext(ValidationErrorContext);

  const error = useMemo(() => questionErrors[question?.linkId], [questionErrors]);
  const existingRules = useMemo(() => question?.validationRule, [question?.validationRule]);

  const [operator, setOperator] = useState(
    existingRules?.operator
      ? responseValidationOptions?.find((option) => option.symbol === existingRules?.operator)
      : { symbol: '', label: '' },
  );
  const [from, setFrom] = useState((existingRules && existingRules['min-value']) || '');
  const [to, setTo] = useState((existingRules && existingRules['max-value']) || '');

  useEffect(() => {
    const [ruleExtension, validationRule] = generateValidationExtensions();
    if (validationRule) {
      handleCreateValidationRule(ruleExtension, validationRule);
    }
  }, [operator, from, to]);

  const handleOperatorChange = (event) => {
    if (event.target.value?.symbol === operator?.symbol) return;
    setOperator(event.target.value);
  };

  const handleFromChange = (event) => {
    let inputNumber = event.target.value;
    const numberCheckRegEx = question.type === 'decimal' ? /^-?\d*\.?\d*$/ : /^-?\d*$/;
    if (numberCheckRegEx.test(inputNumber)) {
      setFrom(inputNumber);
    }
  };

  const handleToChange = (event) => {
    let inputNumber = event.target.value;
    const numberCheckRegEx = question.type === 'decimal' ? /^-?\d*\.?\d*$/ : /^-?\d*$/;
    if (numberCheckRegEx.test(inputNumber)) {
      setTo(inputNumber);
    }
  };

  const handleRemoveValidationRule = () => {
    setOperator({ label: '', symbol: '' });
    setFrom('');
    setTo('');
  };

  const generateValidationExtensions = () => {
    let output = [];
    let validationRule = {};
    switch (operator?.symbol) {
      case '>':
        validationRule = {
          'min-value': from,
          'min-exclusion': true,
          'max-exclusion': false,
        };
        output = [
          { url: `${EXTENSION_BASE_URL}/min-value`, valueDecimal: from },
          { url: `${EXTENSION_BASE_URL}/min-exclusion`, valueBoolean: true },
          { url: `${EXTENSION_BASE_URL}/max-exclusion`, valueBoolean: false },
        ];
        break;
      case '>=':
        validationRule = {
          'min-value': from,
          'min-exclusion': false,
          'max-exclusion': false,
        };
        output = [
          { url: `${EXTENSION_BASE_URL}/min-value`, valueDecimal: from },
          { url: `${EXTENSION_BASE_URL}/min-exclusion`, valueBoolean: false },
          { url: `${EXTENSION_BASE_URL}/max-exclusion`, valueBoolean: false },
        ];
        break;
      case '<':
        validationRule = {
          'max-value': to,
          'min-exclusion': false,
          'max-exclusion': true,
        };
        output = [
          { url: `${EXTENSION_BASE_URL}/max-value`, valueDecimal: to },
          { url: `${EXTENSION_BASE_URL}/min-exclusion`, valueBoolean: false },
          { url: `${EXTENSION_BASE_URL}/max-exclusion`, valueBoolean: true },
        ];
        break;
      case '<=':
        validationRule = {
          'max-value': to,
          'min-exclusion': false,
          'max-exclusion': false,
        };
        output = [
          { url: `${EXTENSION_BASE_URL}/max-value`, valueDecimal: to },
          { url: `${EXTENSION_BASE_URL}/min-exclusion`, valueBoolean: false },
          { url: `${EXTENSION_BASE_URL}/max-exclusion`, valueBoolean: false },
        ];
        break;
      case '<<':
        validationRule = {
          'min-value': from,
          'max-value': to,
          'min-exclusion': false,
          'max-exclusion': false,
        };
        output = [
          { url: `${EXTENSION_BASE_URL}/min-value`, valueDecimal: from },
          { url: `${EXTENSION_BASE_URL}/max-value`, valueDecimal: to },
          { url: `${EXTENSION_BASE_URL}/min-exclusion`, valueBoolean: false },
          { url: `${EXTENSION_BASE_URL}/max-exclusion`, valueBoolean: false },
        ];
        break;
      default:
        validationRule = {};
        console.log('Invalid operator');
    }

    return [output, { ...validationRule, operator: operator?.symbol }];
  };

  return (
    <>
      <Typography sx={{ mt: 4, mb: 1 }}>{strings.responseValidation}</Typography>
      <Stack direction="row" gap={2} alignItems="flex-start" justifyContent="space-between">
        <Grid container direction="row" spacing={2} alignItems="flex-start">
          <Grid item>
            <Select
              size="small"
              value={operator?.label}
              onChange={handleOperatorChange}
              displayEmpty
              renderValue={(selectedValue) => selectedValue || placeholders.noValidation}
              sx={{ minWidth: 200 }}
              error={error && !!error['validationRule.operator']}
            >
              {responseValidationOptions.map((option) => (
                <MenuItem key={option.id} value={option}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {error && !!error['validationRule.operator'] && (
              <FormHelperText error>{error['validationRule.operator']}</FormHelperText>
            )}
          </Grid>
          {(operator?.symbol === '>' || operator?.symbol === '>=' || operator?.symbol === '<<') && (
            <Grid item>
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <FormControl>
                  <TextField
                    sx={{ maxWidth: '100%' }}
                    name="from"
                    value={from}
                    onChange={handleFromChange}
                    placeholder={
                      operator?.symbol === '<<'
                        ? placeholders.from
                        : question.type === 'integer'
                          ? placeholders.integer
                          : placeholders.number
                    }
                    error={error && !!error['validationRule.min-value']}
                    helperText={error && error['validationRule.min-value']}
                    inputProps={{ maxLength: characterLength.responseValidationLength }}
                  />
                  {from.length >= characterLength.responseValidationLength && (
                    <FormHelperText
                      sx={{ textAlign: 'right', color: '#bdc1cc' }}
                    >{`${characterLength.responseValidationLength} / ${characterLength.responseValidationLength}`}</FormHelperText>
                  )}
                </FormControl>
              </Box>
            </Grid>
          )}
          {(operator?.symbol === '<' || operator?.symbol === '<=' || operator?.symbol === '<<') && (
            <Grid item>
              <FormControl>
                <TextField
                  sx={{ maxWidth: '100%' }}
                  name="to"
                  value={to}
                  onChange={handleToChange}
                  placeholder={
                    operator?.symbol === '<<'
                      ? placeholders.to
                      : question.type === 'integer'
                        ? placeholders.integer
                        : placeholders.number
                  }
                  error={error && !!error['validationRule.max-value']}
                  helperText={error && error['validationRule.max-value']}
                  inputProps={{ maxLength: characterLength.responseValidationLength }}
                />
                {to.length >= characterLength.responseValidationLength && (
                  <FormHelperText
                    sx={{ textAlign: 'right', color: '#bdc1cc' }}
                  >{`${characterLength.responseValidationLength} / ${characterLength.responseValidationLength}`}</FormHelperText>
                )}
              </FormControl>
            </Grid>
          )}

          <IconButton onClick={handleRemoveValidationRule} sx={{ m: 2 }}>
            <Close sx={{ fontSize: '20px', cursor: 'pointer' }} />
          </IconButton>
        </Grid>
      </Stack>
    </>
  );
};
