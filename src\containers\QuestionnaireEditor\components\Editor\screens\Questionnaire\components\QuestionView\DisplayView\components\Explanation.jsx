import React, { useMemo } from 'react';
import { Alert, Box, Typography } from '@mui/material';
import { extractExtension } from '@/utility/utils';

export const Explanation = (props) => {
  const { question } = props || {};

  const explanationExt = extractExtension(question.extension, 'Item/explanation');

  const explanationFlagExt = extractExtension(question.extension, 'Item/explanation-flag');

  const explanation = explanationExt !== null ? explanationExt.valueString : '';

  const explanationFlag = explanationFlagExt !== null ? explanationFlagExt.valueString : '';

  const severityOption = {
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info',
  };

  const severity = severityOption[explanationFlag];

  return (
    <>
      {explanation && severity && (
        <Box sx={{ mt: 2, width: '100%' }}>
          <Alert icon={false} severity={severity}>
            <Typography dangerouslySetInnerHTML={{ __html: explanation }} />
          </Alert>
        </Box>
      )}
    </>
  );
};
