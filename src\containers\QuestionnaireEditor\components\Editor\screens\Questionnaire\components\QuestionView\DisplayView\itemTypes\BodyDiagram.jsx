import React from 'react';
import { Box, Stack } from '@mui/material';
import { BodyDiagramSvg } from '@/components';
import { strings } from '@/utility/strings';
import { QuestionText } from '../components/QuestionText';

export const BodyDiagram = (props) => {
  const { question, DescriptionComponent } = props;

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <BodyDiagramSvg />
      </Stack>
    </Box>
  );
};
