import React, { useContext, useState } from 'react';
import {
  TextField,
  Typography,
  Grid,
  IconButton,
  FormControl,
  FormHelperText,
  Select,
  MenuItem,
  FormLabel,
} from '@mui/material';
import { CambianTooltip } from '@/components';
import { placeholders, strings, errors } from '@/utility/strings';
import { characterLength } from '@/containers/CommonConstants';
import { QuestionnaireDetailsContext, ValidationErrorContext } from '@/context';

export const QuestionnaireDetail = (props) => {
  const { questionnaireDetails, handleQuestionnaireDetailsChange } = useContext(QuestionnaireDetailsContext);
  const { questionnaireDetailErrors, handleQuestionnaireDetailValidationErrors } = useContext(ValidationErrorContext);
  console.log({ questionnaireDetailErrors });
  const propertyOptions = [
    { label: 'Patient', type: 'Patient' },
    { label: 'Practitioner', type: 'Practitioner' },
    { label: 'Location', type: 'Location' },
  ];

  const [selectedProperty, setSelectedProperty] = useState(
    questionnaireDetails?.subjectType || propertyOptions[0].type,
  );

  const handlePropertyChange = (event) => {
    const selectedType = event.target.value;
    setSelectedProperty(selectedType);
    handleQuestionnaireDetailsChange({ subjectType: event.target.value });
    console.log(selectedType);
  };

  const questionnaireDetailFields = [
    {
      id: 0,
      label: strings.name,
      required: true,
      component: (
        <FormControl fullWidth>
          <TextField
            fullWidth
            defaultValue={questionnaireDetails?.name}
            name="name"
            onChange={(e) => handleQuestionnaireDetailsChange({ [e.target.name]: e.target.value })}
            onBlur={(event) => {
              let newErrors = { ...questionnaireDetailErrors };
              if (event.target.value.trim().length < 2) {
                newErrors.name = errors.mustBeAtLeast2CharactersLong;
              } else {
                delete newErrors.name;
              }
              handleQuestionnaireDetailValidationErrors(newErrors);
            }}
            error={!!questionnaireDetailErrors?.name}
            helperText={questionnaireDetailErrors?.name}
            placeholder={placeholders.name}
            inputProps={{ maxLength: characterLength.questionnaireNameLength }}
          />
          {questionnaireDetails?.name.length >= characterLength.questionnaireNameLength && (
            <FormHelperText
              sx={{ textAlign: 'right', color: '#bdc1cc' }}
            >{`${characterLength.questionnaireNameLength} / ${characterLength.questionnaireNameLength}`}</FormHelperText>
          )}
        </FormControl>
      ),
      tooltip: strings.questionnaireNameTooltip,
    },
    {
      id: 1,
      label: strings.title,
      required: true,
      component: (
        <FormControl fullWidth>
          <TextField
            size="small"
            defaultValue={questionnaireDetails?.title}
            name="title"
            onChange={(e) => handleQuestionnaireDetailsChange({ [e.target.name]: e.target.value })}
            onBlur={(event) => {
              let newErrors = { ...questionnaireDetailErrors };
              if (event.target.value.trim().length < 2) {
                newErrors.title = errors.mustBeAtLeast2CharactersLong;
              } else {
                delete newErrors.title;
              }
              handleQuestionnaireDetailValidationErrors(newErrors);
            }}
            error={!!questionnaireDetailErrors?.title}
            helperText={questionnaireDetailErrors?.title}
            placeholder={placeholders.title}
            inputProps={{ maxLength: characterLength.questionnaireNameLength }}
          />
          {questionnaireDetails?.title.length >= characterLength.questionnaireNameLength && (
            <FormHelperText
              sx={{ textAlign: 'right', color: '#bdc1cc' }}
            >{`${characterLength.questionnaireNameLength} / ${characterLength.questionnaireNameLength}`}</FormHelperText>
          )}
        </FormControl>
      ),
      tooltip: strings.questionnaireTitleTooltip,
    },

    {
      id: 2,
      label: strings.description,
      required: false,
      component: (
        <FormControl fullWidth>
          <TextField
            defaultValue={questionnaireDetails?.description}
            type="text"
            name="description"
            multiline
            minRows={4}
            placeholder={placeholders.description}
            onChange={(e) => handleQuestionnaireDetailsChange({ [e.target.name]: e.target.value })}
            inputProps={{ maxLength: characterLength.descriptionAndExplanationLength }}
          />
          {questionnaireDetails?.description.length >= characterLength.descriptionAndExplanationLength && (
            <FormHelperText
              sx={{ textAlign: 'right', color: '#bdc1cc' }}
            >{`${characterLength.descriptionAndExplanationLength} / ${characterLength.descriptionAndExplanationLength}`}</FormHelperText>
          )}
        </FormControl>
      ),
      tooltip: strings.questionnaireDescriptionTooltip,
    },
    {
      id: 3,
      label: strings.subjectType,
      required: true,
      component: (
        <FormControl
          fullWidth
          size="small"
          sx={{
            width: { xs: '100%', md: '500px' },
            mt: 1,
            mb: 2,
            pointerEvents: questionnaireDetails.status === 'final' ? 'none' : 'auto',
          }}
        >
          <Select
            value={selectedProperty}
            onChange={handlePropertyChange}
            name="subjectType"
            displayEmpty
            renderValue={(selected) => {
              if (!selected) {
                return <em>{strings.subjectType}</em>;
              }
              return selected;
            }}
          >
            {propertyOptions.map((option) => (
              <MenuItem key={option.type} value={option.type}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      ),
      tooltip: strings.subjectTypeTooltip,
    },
  ];

  return (
    <>
      {questionnaireDetailFields.map((field, index) => (
        <Grid container key={index} sx={{ mb: 2 }}>
          <Grid item xs={12}>
            <CambianTooltip title={<Typography variant="caption">{field.tooltip}</Typography>}>
              <FormLabel required={field.required} sx={{ color: '#000' }}>
                {field.label}
              </FormLabel>
            </CambianTooltip>
          </Grid>
          <Grid item xs={12}>
            {field.component}
          </Grid>
        </Grid>
      ))}
    </>
  );
};
