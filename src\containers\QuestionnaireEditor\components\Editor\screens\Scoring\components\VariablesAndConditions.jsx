import React, { useContext, useCallback, useState, useEffect } from 'react';
import { Typography, Paper, Grid, TextField, Button, FormHelperText, FormControl, FormLabel } from '@mui/material';
import { errors, strings } from '../../../../../../../utility/strings';
import { Add } from '@mui/icons-material';
import { Conditions } from './Conditions';
import { Close } from '@mui/icons-material';
import { ADD_CONDITION, ADD_VARIABLE_NAME, DELETE_VARIABLE } from '@/context/scoring';
import { ScoringContext, QuestionnaireDetailsContext } from '@/context';

export const VariablesAndConditions = (props) => {
  const { HtmlTooltip, variables } = props;
  const { dispatchScoringAction, currentVariable } = useContext(ScoringContext);
  const { questionnaireDetails } = useContext(QuestionnaireDetailsContext);
  const [localVariableName, setLocalVariableName] = useState(currentVariable?.name || '');

  useEffect(() => {
    setLocalVariableName(currentVariable?.name || '');
  }, [currentVariable?.name]);

  console.log('Valriable', currentVariable);
  const handleAddCondition = useCallback(() => {
    dispatchScoringAction({ type: ADD_CONDITION });
  }, [dispatchScoringAction]);

  const handleDeleteVariable = useCallback(() => {
    dispatchScoringAction({ type: DELETE_VARIABLE });
  }, [dispatchScoringAction]);

  const handleAddVariableName = useCallback(
    (event) => {
      const newValue = event.target.value;
      setLocalVariableName(newValue);
      dispatchScoringAction({ type: ADD_VARIABLE_NAME, value: newValue });
    },
    [dispatchScoringAction],
  );

  return (
    <Grid item xs={9}>
      <Paper xs={9} sx={{ mt: 2, ml: 2, p: 2, height: '100%' }}>
        <Grid
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            height: '20px',
          }}
          item
        >
          <Typography>{currentVariable?.name}</Typography>
          {(variables.length > 1 || variables[0]?.formulas[0].selectionRule !== 'Select Rule') && (
            <Close sx={{ fontSize: '20px', mr: 1, cursor: 'pointer' }} onClick={handleDeleteVariable} />
          )}
        </Grid>
        <Grid container sx={{ mt: 2, minHeight: '100%' }}>
          <Grid item sx={{ width: '100%' }}>
            <FormControl error={currentVariable?.variableNameError}>
              <Grid container alignItems="center" spacing={1}>
                <Grid item>
                  <FormLabel required={true} sx={{ mb: 1, color: '#000' }}>
                    {strings.variableName}
                  </FormLabel>
                </Grid>
                <Grid item flex={1}>
                  <TextField
                    sx={{ width: { xs: '100%', md: '500px' }, maxWidth: '100%' }}
                    placeholder={'Variable Name'}
                    onChange={handleAddVariableName}
                    value={localVariableName}
                    error={currentVariable?.variableNameError}
                  />
                </Grid>
              </Grid>
              {currentVariable?.variableNameError && (
                <Grid>
                  <FormHelperText sx={{ ml: 0.3, mt: 0.2, color: '#d32f2f;' }}>
                    {errors.variableNameError}
                  </FormHelperText>
                </Grid>
              )}
            </FormControl>
            {currentVariable?.formulas.map((condition, key) => {
              return (
                <Conditions
                  currentVariable={currentVariable}
                  condition={condition}
                  key={key}
                  keyVal={key}
                  variables={variables}
                  HtmlTooltip={HtmlTooltip}
                />
              );
            })}
            <Grid item>
              <Button
                onClick={handleAddCondition}
                startIcon={<Add fontSize="small" />}
                color="primary"
                sx={{
                  p: '15px 1px',
                  cursor: 'pointer',
                  fontSize: '15px',
                  '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
                }}
              >
                {strings.addCondition}
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  );
};
