import React, { useContext, useState, useMemo, useEffect } from 'react';
import { v4 as uuid } from 'uuid';
import { Stack, Button, Box, Badge, FormControlLabel, Checkbox } from '@mui/material';
import { MoreVert, FileCopy, Delete, Visibility, Book, PublishedWithChanges, Save, Close } from '@mui/icons-material';
import { MenuList } from '@/components';
import { CustomModal } from '@/components/CustomModal';
import { statuses, publishedStatuses } from '@/containers/CommonConstants';
import { errors, strings } from '@/utility/strings';
import { QuestionnaireContext } from '@/context/questionnairePages';
import { validateQuestion, validateQuestionnaireDetail } from '@/utility/validations';
import {
  QuestionnaireDetailsContext,
  QuestionnaireReportContext,
  ValidationErrorContext,
  ScoringContext,
} from '@/context';
import {
  cleanQuestionnaireItem,
  createScoringExtensions,
  decorateWithQuestionAndPageSequence,
  deepCompareObjects,
  downloadFileInJsonFormat,
  extractExtension,
} from '@/utility/utils';
import useNotification from '@/hooks/useNotification';
import { pages } from '@/containers/CommonConstants';
import { CHECK_EMPTY } from '@/context/scoring';
import { useGenerateCodebookData } from '@/hooks/useGenerateCodebookData';

const questionnaireExtension = [
  {
    url: 'display-dial',
    valueBoolean: false,
  },
  {
    url: 'display-description',
    valueBoolean: true,
  },
  {
    url: 'display-large-buttons',
    valueBoolean: false,
  },
  {
    url: 'display-progress-bar',
    valueBoolean: true,
  },
  {
    url: 'display-score',
    valueBoolean: false,
  },
  {
    url: 'display-score-category',
    valueBoolean: false,
  },
  {
    url: 'display-title',
    valueBoolean: true,
  },
  {
    url: 'questionnaire-type',
    valueCode: 'Instrument',
  },
  {
    url: 'question-unit-per-page',
    valueBoolean: true,
  },
  {
    url: 'trendable',
    valueBoolean: false,
  },
  {
    url: 'pdftemplate-id',
    valueString: '',
  },
  {
    url: 'question-identifier-prefix',
    valueString: 'Item',
  },
];

const identifier = [
  {
    use: 'old',
    system: 'questionnaire/identifier',
    value: 'd3ea065c-2d4f-4d38-b4c7-fc4b28d21f78',
    period: {
      start: '2023-12-13T14:06:06+00:00',
      end: '2023-12-13T14:15:33+00:00',
    },
  },
  {
    use: 'usual',
    system: 'urn:uuid',
    value: 'f6356947-59d8-495b-abf7-47973d3a1968',
    period: {
      start: '2023-12-13T14:06:06+00:00',
    },
  },
];

export const QuestionnaireControls = (props) => {
  const {
    handleNavigation,
    onSaveDraftCallback,
    onFinalizeCallback,
    onPublishCallback,
    onDuplicateCallback,
    onDeleteCallback,
    onPreviewCallback,
    handleDownloadCodebookCallback,
    existingQuestionnaireData,
    setExistingQuestionnaire,
    setExistingPdfTemplate,
    publishedRepository = 'no',
  } = props;
  const openSnackbar = useNotification();
  const codeBookData = useGenerateCodebookData(existingQuestionnaireData);

  // contexts
  const { questionnaireState, initialState: initialQuestionnaireState } = useContext(QuestionnaireContext);
  const {
    questionnaireDetails,
    handleQuestionnaireDetailsChange,
    initialState: initialQuestionnaireDetails,
  } = useContext(QuestionnaireDetailsContext);
  const { questionnaireReport, initialState: initialQuestionnaireReport } = useContext(QuestionnaireReportContext);
  const { initialScoringState, scoringState, dispatchScoringAction } = useContext(ScoringContext);
  const { questionErrors, handleQuestionValidationErrors, handleQuestionnaireDetailValidationErrors } =
    useContext(ValidationErrorContext);

  // states
  const [openModal, setOpenModal] = useState(false);
  const [publishStatus, setPublishStatus] = useState({ isPrivate: false, isPublic: false });
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [openFinalConfirmation, setOpenFinalConfirmation] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [isSaved, setIsSaved] = useState(false);

  const isQuestionnaireChanged = useMemo(() => {
    const questionsChanged = !deepCompareObjects(initialQuestionnaireState, questionnaireState);
    const questionnaireDetailChanged = !deepCompareObjects(initialQuestionnaireDetails, questionnaireDetails);
    const questionnaireReportChanged = !deepCompareObjects(initialQuestionnaireReport, questionnaireReport);
    const scoringRulesChanged = !deepCompareObjects(initialScoringState, scoringState);
    return questionsChanged || questionnaireDetailChanged || questionnaireReportChanged || scoringRulesChanged;
  }, [questionnaireState, questionnaireDetails, questionnaireReport, scoringState, existingQuestionnaireData]);

  useEffect(() => {
    setIsSaved(false); // Reset isSaved to false when isQuestionnaireChanged changes
  }, [isQuestionnaireChanged, questionnaireState, questionnaireDetails, questionnaireReport, scoringState]);

  const checkScoringFields = () => {
    const fields = scoringState.filter((item) => {
      const result = item.formulas.some((formula) => {
        return scoringState.length === 1
          ? formula.selectionRule !== 'Select Rule' && formula.mathematicalExpression.trim() === ''
          : formula.mathematicalExpression.trim() === '';
      });
      return result;
    });
    return fields;
  };
  const getFieldsWithEmptyName = () => {
    const fieldsWithEmptyName = scoringState.filter((item) => item.variableNameError === true);
    return fieldsWithEmptyName;
  };
  const findHighestItemId = (questionnaireState) => {
    let highestItemId = 0;
    questionnaireState.forEach((page) => {
      page.item.forEach((item) => {
        const currentItemId = parseInt(item.linkId.replace('Item', ''), 10);
        if (!isNaN(currentItemId)) {
          highestItemId = Math.max(highestItemId, currentItemId);
        }
      });
    });

    return highestItemId;
  };

  const checkMathematicalExpressionErrorPresent = () => {
    const fields = scoringState.filter((item) => {
      const result = item.formulas.some((formula) => {
        return formula.selectionRule !== 'Select Rule' && formula.mathematicalExpressionError;
      });
      return result;
    });

    return fields;
  };

  const handleCheckForDuplicates = () => {
    const uniqueNames = new Set();

    for (const obj of scoringState) {
      if (uniqueNames.has(obj.name)) {
        openSnackbar({ variant: 'error', msg: `Duplicate Variable name: ${obj.name} is not allowed.` });
        return true; // Duplicate name found
      }
      uniqueNames.add(obj.name);
    }
    return false;
  };

  const generateFhirQuestionnaire = async () => {
    const questionValidationResult = await Promise.all(
      questionnaireState.flatMap((page, pageIndex) =>
        page.item.map(async (item, itemIndex) => {
          const error = await validateQuestion(item, itemIndex);
          return { [item.linkId]: error };
        }),
      ),
    );

    const questionsError = questionValidationResult?.reduce((acc, item) => {
      const key = Object.keys(item)[0];
      acc[key] = item[key];
      return acc;
    }, {});

    const emptyFieldsArr = checkScoringFields();
    const emptyNameFieldError = getFieldsWithEmptyName();
    const mathematicalExpressionErrorArr = checkMathematicalExpressionErrorPresent();
    const duplicateVariableNames = handleCheckForDuplicates();

    handleQuestionValidationErrors(questionsError);

    const questionnaireDetailErrors = await validateQuestionnaireDetail(questionnaireDetails);
    handleQuestionnaireDetailValidationErrors(questionnaireDetailErrors);

    if (emptyNameFieldError.length > 0) {
      const message = `${errors.variableNameError}`;
      openSnackbar({ variant: 'error', msg: message });
    }
    if (emptyFieldsArr.length > 0) {
      dispatchScoringAction({ type: CHECK_EMPTY });
      const message = `${errors.equationField} ${emptyFieldsArr[0].name}`;
      openSnackbar({ variant: 'error', msg: message });
    }

    if (
      emptyFieldsArr.length === 0 &&
      !duplicateVariableNames &&
      mathematicalExpressionErrorArr.length === 0 &&
      emptyNameFieldError.length === 0
    ) {
      if (
        Object.values(questionsError).every((subObj) => Object.keys(subObj).length === 0) &&
        Object.values(questionnaireDetailErrors).length === 0
      ) {
        let decoratedWithSequence = decorateWithQuestionAndPageSequence(questionnaireState);
        const cleanedItems = cleanQuestionnaireItem(decoratedWithSequence);

        const formattedScores = createScoringExtensions(scoringState);

        const pdfTemplate = questionnaireReport?.reportPdfTemplate?.fileBase64 || '';
        const highestItemId = findHighestItemId(questionnaireState);
        const nextItemId = highestItemId + 1;

        const updatedExtension = [
          ...questionnaireExtension,
          { url: 'codeBookHtmlData', valueString: questionnaireReport?.codeBookHtml || '' },
          { url: 'question-identifier-next-sequence', valueInteger: nextItemId },
          { url: 'htmltemplate-base64', valueString: questionnaireReport?.reportHtml || '' },
          { url: 'pdftemplate-name', valueString: questionnaireReport?.reportPdfTemplate?.fileName || '' },
          // { url: 'pdftemplate-base64', valueString: questionnaireReport?.reportPdfTemplate?.fileBase64 || '' }, // As per changes in AR, now need to send pdf base64 outside of questionnaire
          ...formattedScores,
        ];

        let fhirQuestionnaire = existingQuestionnaireData ? structuredClone(existingQuestionnaireData) : {};
        fhirQuestionnaire = {
          ...fhirQuestionnaire,
          resourceType: 'Questionnaire',
          date: new Date().toISOString(),
          name: questionnaireDetails?.name,
          title: questionnaireDetails?.title,
          description: questionnaireDetails?.description || null,
          subjectType: questionnaireDetails?.subjectType,
          extension: updatedExtension,
          identifier,
          item: cleanedItems,
          name: questionnaireDetails?.name,
          publisher: 'App-Scoop',
          status: questionnaireDetails?.status || 'draft',
        };

        if (!existingQuestionnaireData) {
          fhirQuestionnaire.id = uuid();
          fhirQuestionnaire.url = `Questionnaire/${fhirQuestionnaire?.id}`;
          handleQuestionnaireDetailsChange({
            ...questionnaireDetails,
            id: fhirQuestionnaire?.id,
            url: `Questionnaire/${fhirQuestionnaire?.id}`,
            status: 'draft',
          });
        }

        return { fhirQuestionnaire, pdfTemplate };
      }
    }
  };

  const handlePublishConfirm = async () => {
    let publishedStatus = '';
    if (publishStatus.isPrivate && !publishStatus.isPublic) {
      publishedStatus = publishedStatuses.private;
    } else if (!publishStatus.isPrivate && publishStatus.isPublic) {
      publishedStatus = publishedStatuses.public;
    } else if (publishStatus.isPrivate && publishStatus.isPublic) {
      publishedStatus = publishedStatuses.both;
    } else if (!publishStatus.isPrivate && !publishStatus.isPublic) {
      publishedStatus = publishedStatuses.no;
    }
    const { fhirQuestionnaire } = await generateFhirQuestionnaire();
    if (fhirQuestionnaire) {
      const { success, message } = await onPublishCallback(fhirQuestionnaire.id, publishedStatus);
      if (success) {
        handleNavigation(pages.questionnaireList);
        openSnackbar({ variant: 'error', msg: message });
      } else {
        openSnackbar({ variant: 'error', msg: message });
      }
    }
    setOpenModal(false);
  };

  const handleDelete = async () => {
    const { success, message } = await onDeleteCallback(existingQuestionnaireData?.id, publishedRepository || 'no');
    if (success) {
      handleNavigation(pages.questionnaireList);
    } else {
      openSnackbar({ variant: 'error', msg: message });
    }
  };

  const handleSaveDraft = async () => {
    if (
      questionnaireState[0]?.item?.length === 0 ||
      questionnaireState.length === 0 ||
      questionnaireState[0]?.item[0]?.type === ''
    ) {
      openSnackbar({ variant: 'error', msg: errors.atLeastOneQuestionInTheQuestionnaire });
      return;
    }
    const { fhirQuestionnaire, pdfTemplate } = await generateFhirQuestionnaire();

    if (fhirQuestionnaire) {
      const { success, message, questionnaire } = (await onSaveDraftCallback(fhirQuestionnaire, pdfTemplate)) || {};
      if (success) {
        openSnackbar({ variant: 'info', msg: message });
        setExistingQuestionnaire(questionnaire);
        setExistingPdfTemplate(pdfTemplate);
        setIsSaved(true);
      } else {
        openSnackbar({ variant: 'error', msg: message });
      }
      return { success, questionnaire: questionnaire };
    }

    return { success: false, questionnaire: fhirQuestionnaire };
  };

  const handleFinalizeConfirm = async () => {
    if (
      questionnaireState[0]?.item?.length === 0 ||
      questionnaireState.length === 0 ||
      questionnaireState[0]?.item[0]?.type === ''
    ) {
      openSnackbar({ variant: 'error', msg: errors.atLeastOneQuestionInTheQuestionnaire });
      return;
    }
    setOpenFinalConfirmation(true);
  };

  const handleFinalize = async () => {
    setOpenFinalConfirmation(false);
    const { fhirQuestionnaire, pdfTemplate } = await generateFhirQuestionnaire();
    if (fhirQuestionnaire) {
      const { success, message, questionnaire } = await onFinalizeCallback(fhirQuestionnaire, pdfTemplate);
      if (success) {
        handleQuestionnaireDetailsChange({ ...questionnaireDetails, status: questionnaire?.status });
        setExistingQuestionnaire(questionnaire);
        setExistingPdfTemplate(pdfTemplate);
        openSnackbar({ variant: 'error', msg: message });
      } else {
        openSnackbar({ variant: 'error', msg: message });
      }
    } else {
      openSnackbar({ variant: 'error', msg: 'Cannot finalize. Please try again.' });
    }
  };

  const handleDuplicate = async () => {
    const { fhirQuestionnaire } = await generateFhirQuestionnaire();

    if (existingQuestionnaireData?.id && !isQuestionnaireChanged) {
      const { success, message } = await onDuplicateCallback(
        fhirQuestionnaire?.id || existingQuestionnaireData?.id,
        publishedRepository || 'no',
      );
      if (success) {
        console.log('Questionnaire has been duplicated.');
      }
    } else {
      const { success, questionnaire } = await handleSaveDraft();
      if (success) {
        const { success, message } = await onDuplicateCallback(
          questionnaire?.id || fhirQuestionnaire?.id,
          publishedRepository || 'no',
        );
        if (success) {
          console.log('Questionnaire has been saved and duplicated.');
        }
      } else {
        openSnackbar({ variant: 'error', msg: 'Questionnaire cannot be duplicated.' });
      }
    }
  };

  const handlePreview = async () => {
    if (existingQuestionnaireData?.id && (!isQuestionnaireChanged || isSaved)) {
      onPreviewCallback(existingQuestionnaireData?.id, publishedRepository || 'no');
    } else {
      const { success, questionnaire } = await handleSaveDraft();
      if (success) {
        onPreviewCallback(questionnaire?.id, publishedRepository || 'no');
      } else {
        openSnackbar({ variant: 'error', msg: 'Something went wrong, please try again later' });
      }
    }
  };

  const handleExportQuestionnaire = async () => {
    const { fhirQuestionnaire } = await generateFhirQuestionnaire();
    downloadFileInJsonFormat(JSON.stringify(fhirQuestionnaire), fhirQuestionnaire.name);
    console.log('Exported Questionnaire!', fhirQuestionnaire);
  };

  const handleMenuIconClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleDownloadCodebook = async () => {
    const { fhirQuestionnaire } = await generateFhirQuestionnaire();
    if (existingQuestionnaireData?.id) {
      const { success, message } =
        (await handleDownloadCodebookCallback(
          fhirQuestionnaire?.id || existingQuestionnaireData?.id,
          publishedRepository || 'no',
          codeBookData,
        )) || {};
      if (success) {
        console.log('Codebook has been dowloaded!');
      }
    } else {
      const { success, questionnaire } = await handleSaveDraft();

      if (success) {
        const { success: downloadSuccess, message } =
          (await handleDownloadCodebookCallback(
            questionnaire?.id || fhirQuestionnaire?.id,
            publishedRepository || 'no',
            codeBookData,
          )) || {};
        if (downloadSuccess) {
          console.log('Codebook has been downloaded!');
        }
      } else {
        openSnackbar({ variant: 'error', msg: 'Codebook cannot be downloaded. Please try again.' });
      }
    }
  };

  const menuItems = [
    {
      id: 0,
      label: strings.preview,
      icon: <Visibility sx={{ fontSize: '20px', mr: 1 }} />,
      handleClick: handlePreview,
      show: true,
    },
    {
      id: 1,
      label: strings.duplicate,
      icon: <FileCopy sx={{ fontSize: '20px', mr: 1 }} />,
      handleClick: handleDuplicate,
      show: true,
    },
    {
      id: 2,
      label: strings.export,
      icon: <Save sx={{ fontSize: '20px', mr: 1 }} />,
      handleClick: handleExportQuestionnaire,
      show: true,
    },
    {
      id: 3,
      label: strings.codeBook,
      icon: <Book sx={{ fontSize: '20px', mr: 1 }} />,
      handleClick: handleDownloadCodebook,
      show: true,
    },
    {
      id: 3,
      label: strings.publish,
      icon: <PublishedWithChanges sx={{ fontSize: '20px', mr: 1 }} />,
      handleClick: () => {
        setOpenModal(true);
        if (publishedRepository === 'private') {
          setPublishStatus({ isPrivate: true, isPublic: false });
        } else if (publishedRepository === 'public') {
          setPublishStatus({ isPrivate: false, isPublic: true });
        } else if (publishedRepository === 'both') {
          setPublishStatus({ isPrivate: true, isPublic: true });
        } else {
          setPublishStatus({ isPrivate: false, isPublic: false });
        }
      },
      show: questionnaireDetails?.status === statuses.final,
    },
    {
      id: 4,
      label: strings.delete,
      icon: <Delete sx={{ fontSize: '20px', mr: 1 }} />,
      handleClick: () => {
        if (questionnaireDetails.id === '') {
          alert(`Error deleting questionnaire: Only saved questionnaire can be deleted.`);
        } else {
          setOpenDeleteConfirmation(true);
        }
      },
      show: true,
    },
  ];

  const publishModalContent = (
    <>
      <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
        <FormControlLabel
          control={
            <Checkbox
              checked={publishStatus.isPrivate}
              onChange={(e) => setPublishStatus({ ...publishStatus, isPrivate: e.target.checked })}
            />
          }
          label={strings.private}
          sx={{ mr: 4 }}
        />
        <FormControlLabel
          control={
            <Checkbox
              checked={publishStatus.isPublic}
              onChange={(e) => setPublishStatus({ ...publishStatus, isPublic: e.target.checked })}
            />
          }
          label={strings.public}
        />
      </Box>
    </>
  );

  return (
    <Box>
      <MenuList menuItems={menuItems} anchorEl={anchorEl} setAnchorEl={setAnchorEl} />
      <Stack direction="row" spacing={2} justifyContent="space-between" alignItems="center">
        <Stack direction="row" alignItems="center" spacing={2}>
          <Stack>{questionnaireDetails?.name || strings.untitledQuestionnaire}</Stack>
          {questionnaireDetails?.status && (
            <Badge
              badgeContent={questionnaireDetails?.status === 'final' ? strings.finalBadge : strings.draftBadge}
              color={questionnaireDetails?.status === statuses.final ? 'success' : 'info'}
              sx={{ paddingLeft: '8px' }}
            />
          )}
        </Stack>
        <Stack direction="row" spacing={2} alignItems="center">
          <Button variant="contained" onClick={handleSaveDraft} disabled={!isQuestionnaireChanged || isSaved}>
            {strings.save}
          </Button>
          {questionnaireDetails?.status !== statuses.final && (
            <Button variant="outlined" onClick={handleFinalizeConfirm}>
              {strings.finalize}
            </Button>
          )}
          <MoreVert sx={{ cursor: 'pointer', color: 'rgba(0, 0, 0, 0.54)' }} onClick={handleMenuIconClick} />
        </Stack>
      </Stack>
      <CustomModal
        open={openModal}
        onClose={() => setOpenModal(false)}
        onConfirm={handlePublishConfirm}
        title={strings.publishConfirmation}
        subTitle={strings.selectRepositoryYouWantToPublishIn}
        content={publishModalContent}
        saveButtonText={strings.publish}
        closeButtonText={strings.cancel}
      />
      <CustomModal
        open={openFinalConfirmation}
        onClose={() => setOpenFinalConfirmation(false)}
        onConfirm={handleFinalize}
        title={strings.finalizeDialogueQuestionTitle}
        subTitle={strings.finalizeDialogueQuestionText}
        saveButtonText={strings.finalize}
        closeButtonText={strings.cancel}
      />
      <CustomModal
        open={openDeleteConfirmation}
        onClose={() => setOpenDeleteConfirmation(false)}
        onConfirm={handleDelete}
        title={strings.deleteQuestionnaire}
        subTitle={strings.thisActionCanNotBeUndone}
        saveButtonText={strings.delete}
        closeButtonText={strings.cancel}
      />
    </Box>
  );
};
