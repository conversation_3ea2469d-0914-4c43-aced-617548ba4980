import React from 'react';
import { useContext, useMemo } from 'react';
import { Paper, Stack, Typography, Divider, Box } from '@mui/material';
import ListAltIcon from '@mui/icons-material/ListAlt';
import { strings } from '@/utility/strings';
import { QuestionnaireContext } from '@/context';

export const PagesAndQuestionsList = () => {
  const { questionnaireState } = useContext(QuestionnaireContext);
  const noItemPresent = useMemo(() => {
    let isNoItemPresent = true;

    for (const page of questionnaireState) {
      if (page?.item?.length >= 2) {
        isNoItemPresent = page.item.every((item) => !item.text);
      } else if (page?.item?.length === 1) {
        isNoItemPresent = !page.item[0]?.text;
      }
      if (!isNoItemPresent) {
        break;
      }
    }

    return isNoItemPresent;
  }, [questionnaireState]);

  return (
    <Paper sx={{ height: 430, overflowY: 'auto' }}>
      <Stack direction="column" divider={<Divider />} sx={{ height: '100%' }}>
        <Stack direction="row" alignItems="center">
          <ListAltIcon color="primary" sx={{ pl: 1, fontSize: '20px' }} />
          <Typography variant="h3" sx={{ p: 1 }}>
            {strings.items}
          </Typography>
        </Stack>
        <Box sx={{ width: '100%', height: '100%' }}>
          {!noItemPresent ? (
            questionnaireState?.map((page, pageIndex) => (
              <div key={pageIndex}>
                <Typography sx={{ pl: 2, pt: 1 }}>
                  {strings.page} {pageIndex + 1}
                </Typography>
                <ul style={{ marginTop: 0.5 }}>
                  {page?.item?.length &&
                    page?.item?.map(
                      (question, questionIndex) => !question.isEditMode && <li key={questionIndex}>{question.text}</li>,
                    )}
                </ul>
              </div>
            ))
          ) : (
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Typography>{strings.getStartedByAddingItem}</Typography>
            </Box>
          )}
        </Box>
      </Stack>
    </Paper>
  );
};
