import React from 'react';
import { QuestionnaireScreen } from './screens/Questionnaire';
import { editorScreens } from '@/containers/CommonConstants';
import { Properties } from './screens/Properties';
import { Scoring } from './screens/Scoring';
import { Box } from '@mui/material';
import { ReportPDFTemplate } from './screens/ReportPDFTemplate';
import { ReportHTMLTemplate } from './screens/ReportHTMLTemplate';
import { CodeBook } from './screens/CodeBook';

export const Editor = (props) => {
  const {
    currentScreen,
    items,
    setItems,
    getQuestionnaireDetails,
    codebookDefaultTemplate,
    htmlReportDefaultTemplate,
  } = props;
  console.log({ currentScreen });

  const getEditorScreen = () => {
    let page;

    switch (currentScreen) {
      case editorScreens.PROPERTIES:
        page = <Properties getQuestionnaireDetails={getQuestionnaireDetails} />;
        break;
      case editorScreens.ITEMS:
        page = <QuestionnaireScreen setItems={setItems} />;
        break;
      case editorScreens.SCORING:
        page = <Scoring items={items} />;
        break;
      case editorScreens.REPORTS:
        page = <ReportHTMLTemplate htmlReportDefaultTemplate={htmlReportDefaultTemplate} />;
        break;
      case editorScreens.HTML_TEMPLATE:
        page = <ReportHTMLTemplate htmlReportDefaultTemplate={htmlReportDefaultTemplate} />;
        break;
      case editorScreens.PDF_TEMPLATE:
        page = <ReportPDFTemplate />;
        break;
      case editorScreens.CODE_BOOK:
        page = <CodeBook codebookDefaultTemplate={codebookDefaultTemplate} />;
        break;
      default:
        page = <>Not Found</>;
        break;
    }

    return page;
  };

  return <Box>{getEditorScreen()}</Box>;
};
