import React from 'react';
import { Box, Grid, Stack } from '@mui/material';
import { strings } from '@/utility/strings';
import { LARGE_BUTTON_COLORS } from '@/containers/CommonConstants';
import { QuestionText } from '../components/QuestionText';

export const LargeButton = (props) => {
  const { question, DescriptionComponent } = props;

  const getLargeButtonStyle = (index) => ({
    minHeight: 100,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: '#fff',
    backgroundColor: Object.values(LARGE_BUTTON_COLORS)[index],
  });

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <Grid container spacing={2}>
          {question?.answerOption.map((option, index) => (
            <Grid key={index} item xs={12} sm={6}>
              <Box sx={getLargeButtonStyle(index)}>{option.valueCoding.display}</Box>
            </Grid>
          ))}
        </Grid>
      </Stack>
    </Box>
  );
};
