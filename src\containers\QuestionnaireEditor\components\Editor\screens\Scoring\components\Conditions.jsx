import React, { useState, useContext, useEffect } from 'react';
import {
  Typography,
  Button,
  Grid,
  Paper,
  FormControl,
  Select,
  MenuItem,
  TextField,
  FormHelperText,
  Modal,
  Box,
  FormLabel,
} from '@mui/material';
import { selectionNames } from '@/containers/CommonConstants';
import { strings, placeholders } from '@/utility/strings';
import HelpIcon from '@mui/icons-material/Help';
import { Close } from '@mui/icons-material';
import { useDrop } from 'react-dnd';
import { ScoringModal } from './ScoringModal';
import { ScoringContext } from '@/context';
import { QuestionnaireContext } from '@/context';
import { CustomModal } from '@/components/CustomModal';
import {
  SELECT_EQUATION,
  ADD_ARITHMETIC,
  ADD_ITEM_NAME,
  ADD_ITEM_NAME_EQUATION,
  ADD_EQUATION,
  DELETE_CONDITION,
} from '@/context/scoring';
import { CambianTooltip } from '@/components';

export const Conditions = (props) => {
  const { condition, keyVal, HtmlTooltip, items } = props;
  const { scoringState: variables, dispatchScoringAction, currentVariable } = useContext(ScoringContext);
  const { questionnaireState: questionnairePages } = useContext(QuestionnaireContext);
  const [leastSelected, setLeastSelected] = useState(0);
  const [selectedCondition, setSelectedConditions] = useState(0);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const handleOpenDeleteModal = () => setOpenDeleteModal(true);
  const handleCloseDeleteModal = () => setOpenDeleteModal(false);
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
  };

  const requiredItemsCount = (inputString, targetCharacter) => {
    const regex = new RegExp(targetCharacter, 'g');
    const matches = inputString.match(regex);

    return matches ? matches.length : 0;
  };

  let requiredCount = 0;
  let requiredCountSubset = 0;

  if (condition.selectionName === selectionNames.requiredResponsesCountFromSubset) {
    const match = condition.selectionRule.match(/\$[^$]*\$(.*)/);
    requiredCountSubset = requiredItemsCount(match[1], '1');
  }

  if (condition.selectionName === selectionNames.requiredResponses) {
    requiredCount = requiredItemsCount(condition.selectionRule, '1');
  }

  const conditionHeader = {
    backgroundColor: '#D9DAD9',
    height: '34px',
    borderRadius: '4px 4px 0 0',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  };

  const modalCloseStyles = {
    border: 'none',
    background: 'none',
    color: '#4d7992',
    mt: 1,
    ml: 2,
    padding: 0,
    height: '10',
    cursor: 'pointer',
  };

  const equationStyles = {
    fontSize: '16px',
    marginTop: 3,
    marginBottom: 2,
  };

  const deleteConditionModalStyle = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 600,
    bgcolor: 'background.paper',
    boxShadow: '0 5px 15px rgba(0, 0, 0, 0.5)',
    borderRadius: '6px',
    p: 1,
  };

  const EquationToolTip = (
    <CambianTooltip
      size="500"
      placement="left-start"
      title={
        <div style={{ minWidth: '500' }}>
          <FormLabel required={true} sx={{ mt: 1, color: '#000' }}>
            {strings.equation}
          </FormLabel>
          <p>{strings.equationToolTip1}</p>
          <p>{strings.equationToolTip2}</p>
          <p>{strings.equationToolTip3}</p>
          <p>{strings.equationToolTip4}</p>
          <p>{strings.equationToolTip5}</p>
        </div>
      }
    >
      <HelpIcon style={{ color: '#0000008A', fontSize: '20px', marginLeft: 4 }} />
    </CambianTooltip>
  );

  const ArithimicComparisonTooltip = (
    <CambianTooltip
      size="300"
      title={
        <div>
          <Typography>{strings.arithmeticComparison}</Typography>
          <p>{strings.ArithmeticToolTip}</p>
        </div>
      }
    >
      <HelpIcon style={{ color: '#0000008A', fontSize: '20px', marginTop: '8px', marginLeft: 4 }} />
    </CambianTooltip>
  );

  return (
    <>
      <Grid key={keyVal} item sx={{ mt: 2 }}>
        <Paper>
          <div style={conditionHeader}>
            <Typography sx={{ fontSize: '16px', padding: '0 15px' }}>{'Condition ' + (keyVal + 1)}</Typography>
            {keyVal > 0 && (
              <Close sx={{ fontSize: '20px', mr: 1, cursor: 'pointer' }} onClick={() => handleOpenDeleteModal()} />
            )}
          </div>
          <Grid container sx={{ p: 2, width: '100%' }} direction="column" justifyContent="center">
            <Grid>
              <FormLabel required={true} sx={{ mb: 1, color: '#000' }}>
                {strings.selectionRule}
              </FormLabel>
            </Grid>
            <Grid container>
              <Grid
                item
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'center',
                }}
              >
                <FormControl
                  fullWidth
                  size="small"
                  sx={{ width: { xs: '100%', md: '500px' }, mt: 1 }}
                  error={condition.selectionRuleError}
                >
                  <Select
                    displayEmpty
                    labelId="demo-simple-select-error-label"
                    value={condition.selectionName}
                    onChange={(event) =>
                      dispatchScoringAction({
                        type: SELECT_EQUATION,
                        key: keyVal,
                        value: event.target.value,
                        items: questionnairePages,
                      })
                    }
                    renderValue={(selectedValue) => selectedValue || placeholders.selectRule}
                    sx={{ marginBottom: 0.3 }}
                  >
                    <MenuItem value={selectionNames.arithmeticComparison}>{strings.arithmeticComparison}</MenuItem>
                    <MenuItem value={selectionNames.requiredResponses}>{strings.requiredResponses}</MenuItem>
                    <MenuItem value={selectionNames.requiredResponsesCountFromSubset}>
                      {strings.requiredResponsesSubset}
                    </MenuItem>
                  </Select>
                  {condition.selectionRuleError && (
                    <FormHelperText sx={{ ml: 0.3, mt: 0, mb: 1 }}>{strings.selectError}</FormHelperText>
                  )}
                </FormControl>
                {(condition.selectionName === selectionNames.requiredResponses ||
                  condition.selectionName === selectionNames.requiredResponsesCountFromSubset) && (
                  <Typography onClick={handleOpen} sx={modalCloseStyles}>
                    {strings.selectMandatoryItems}
                  </Typography>
                )}
                {condition.selectionName === selectionNames.arithmeticComparison && (
                  <Typography sx={{ marginTop: 2, marginLeft: 2 }}>{strings.IF}</Typography>
                )}
              </Grid>
              {condition.selectionName === selectionNames.arithmeticComparison && (
                <Grid container direction="column" justifyContent="flexStart">
                  <Grid item sx={{ display: 'flex', flexDirection: 'row', mt: 0.5, justifyContent: 'space-between' }}>
                    <DraggableArithmeticTextBox
                      keyVal={keyVal}
                      variables={variables}
                      dispatchScoringAction={dispatchScoringAction}
                      value={condition.arithmetic}
                    />
                    {ArithimicComparisonTooltip}
                  </Grid>
                  <Grid container justifyContent="center" direction="column">
                    <FormLabel required={true} sx={{ mt: 1, color: '#000' }}>
                      {strings.equation}
                    </FormLabel>
                    <Grid item sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                      <label style={{ fontSize: '12px', fontWeight: 'normal', marginTop: '6px' }}>
                        {strings.newVariable}
                      </label>
                    </Grid>
                    <Grid
                      item
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}
                    >
                      <DraggableEquationTextBox
                        keyVal={keyVal}
                        error={condition.mathematicalExpressionError}
                        strings={strings}
                        variables={variables}
                        dispatchScoringAction={dispatchScoringAction}
                        value={condition.mathematicalExpression}
                      />
                      {EquationToolTip}
                    </Grid>
                  </Grid>
                </Grid>
              )}

              {(condition.selectionName === selectionNames.requiredResponses ||
                condition.selectionName === selectionNames.requiredResponsesCountFromSubset) && (
                <Grid container direction="column" justifyContent="flexStart">
                  <Grid container justifyContent="center" direction="column">
                    <Grid
                      item
                      sx={{ marginBottom: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start' }}
                    >
                      {(requiredCountSubset > 0 || requiredCount > 0) && (
                        <label style={{ fontSize: '16px', marginTop: 3, marginBottom: 2 }}>
                          {` ${strings.condition}`}
                        </label>
                      )}
                      {condition.selectionName === selectionNames.requiredResponsesCountFromSubset &&
                        requiredCountSubset > 0 && (
                          <label
                            style={{
                              fontWeight: 'normal',
                              fontSize: '12px',
                              marginTop: 3,
                              marginBottom: 2,
                              marginLeft: 4,
                            }}
                          >
                            {`  ${condition.subSetCount} of the ${requiredCountSubset} ${strings.itemsReq}`}
                          </label>
                        )}
                      {condition.selectionName === selectionNames.requiredResponses && requiredCount > 0 && (
                        <label
                          style={{
                            fontWeight: 'normal',
                            fontSize: '12px',
                            marginTop: 3,
                            marginBottom: 2,
                            marginLeft: 4,
                          }}
                        >
                          {` ${requiredCount} ${strings.itemsReq}`}
                        </label>
                      )}
                    </Grid>
                    <FormLabel required={true} sx={{ mt: 1, color: '#000' }}>
                      {strings.equation}
                    </FormLabel>
                    <Grid item sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                      <label style={{ fontSize: '12px', fontWeight: 'normal', marginTop: '8px' }}>
                        {strings.newVariable}
                      </label>
                    </Grid>
                    <Grid
                      item
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}
                    >
                      <DraggableEquationTextBox
                        error={condition.mathematicalExpressionError}
                        strings={strings}
                        keyVal={keyVal}
                        variables={variables}
                        dispatchScoringAction={dispatchScoringAction}
                        value={condition.mathematicalExpression}
                      />
                      {EquationToolTip}
                    </Grid>
                  </Grid>
                </Grid>
              )}
            </Grid>
          </Grid>
        </Paper>
      </Grid>
      <CustomModal
        open={openDeleteModal}
        onClose={() => handleCloseDeleteModal()}
        onConfirm={() => {
          dispatchScoringAction({ type: DELETE_CONDITION, key: keyVal });
          handleCloseDeleteModal();
        }}
        title={strings.deleteFormula}
        subTitle={strings.thisActionCanNotBeUndone}
        saveButtonText={strings.delete}
        closeButtonText={strings.cancel}
      />
      <ScoringModal
        open={open}
        handleClose={handleClose}
        currentVariable={currentVariable}
        variable={variables}
        condition={condition}
        keyVal={keyVal}
        items={items}
        leastSelected={leastSelected}
        setLeastSelected={setLeastSelected}
        selectedCondition={selectedCondition}
        setSelectedConditions={setSelectedConditions}
      />
    </>
  );
};

const DraggableEquationTextBox = React.memo((props) => {
  const { keyVal, value, dispatchScoringAction, strings, error } = props;
  const [localValue, setLocalValue] = useState(value || '');
  useEffect(() => {
    setLocalValue(value || '');
  }, [value]);

  const [{ isOver }, drop] = useDrop({
    accept: 'TEXT',
    drop: (item) => {
      let newItem = { text: item.text };
      dispatchScoringAction({ type: ADD_ITEM_NAME_EQUATION, key: keyVal, item: newItem });

      return undefined;
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  });

  const handleChange = (event) => {
    const newValue = event.target.value;
    setLocalValue(newValue);
    dispatchScoringAction({ type: ADD_EQUATION, key: keyVal, value: newValue });
  };

  return (
    <TextField
      sx={{ maxWidth: '100%' }}
      error={error}
      helperText={error ? strings.selectError : null}
      ref={drop}
      value={localValue}
      onChange={handleChange}
    ></TextField>
  );
});

const DraggableArithmeticTextBox = React.memo((props) => {
  const { keyVal, value, dispatchScoringAction, variables, setVariables } = props;
  const [localValue, setLocalValue] = useState(value || '');
  useEffect(() => {
    setLocalValue(value || '');
  }, [value]);

  const [{ isOver }, drop] = useDrop({
    accept: 'TEXT',
    drop: (item) => {
      let newItem = { text: item.text };
      dispatchScoringAction({ type: ADD_ITEM_NAME, key: keyVal, item: newItem });

      return undefined;
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  });

  const handleChange = (event) => {
    const newValue = event.target.value;
    setLocalValue(newValue);
    dispatchScoringAction({ type: ADD_ARITHMETIC, value: newValue, key: keyVal });
  };

  return <TextField ref={drop} value={localValue} onChange={handleChange} sx={{ mb: 3, maxWidth: '100%' }}></TextField>;
});
