import React from 'react';
import { Box, Stack, TextField } from '@mui/material';
import { strings } from '@/utility/strings';
import { dateTimeFormats } from '@/containers/CommonConstants';
import { QuestionText } from '../components/QuestionText';

export const DatePicker = (props) => {
  const { question, DescriptionComponent } = props;

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <TextField
          placeholder={
            question?.type === 'date' ? dateTimeFormats.placeholders.date : dateTimeFormats.placeholders.dateTime
          }
        />
      </Stack>
    </Box>
  );
};
