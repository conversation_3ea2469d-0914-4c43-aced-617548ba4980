import React from 'react';
import { Box, Stack, TextField } from '@mui/material';
import { strings } from '@/utility/strings';
import { QuestionText } from '../components/QuestionText';

export const DatePicker = (props) => {
  const { question, DescriptionComponent } = props;

  return (
    <Box>
      <Stack gap={2}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <TextField placeholder={question?.type === 'date' ? 'yyyy-mm-dd' : 'yyyy-mm-dd hh:mm aa'} />
      </Stack>
    </Box>
  );
};
