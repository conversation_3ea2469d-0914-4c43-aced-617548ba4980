import React from 'react';
import { Box, Grid, Stack, Table, TableBody, TableCell, TableRow, Typography } from '@mui/material';
import { extractExtension } from '@/utility/utils';
import { QuestionText } from '../components/QuestionText';

export const LinearScale = (props) => {
  const { question, DescriptionComponent } = props;

  const range = question.answerOption.length + 1;
  const startLabel = extractExtension(question.extension, 'Item/bar-start-label')?.valueString;
  const endLabel = extractExtension(question.extension, 'Item/bar-end-label')?.valueString;

  return (
    <Box>
      <Stack gap={2} sx={{ pointerEvents: 'none' }}>
        <QuestionText questionText={question.text} />
        {DescriptionComponent}
        <Grid container>
          <Grid item xs={12} sx={{ overflowX: 'auto', whiteSpace: 'nowrap' }}>
            <Stack direction="row" justifyContent="space-between" sx={{ mb: 1 }}>
              <Typography dangerouslySetInnerHTML={{ __html: startLabel }} />
              <Typography dangerouslySetInnerHTML={{ __html: endLabel }} />
            </Stack>
            <Table>
              <TableBody>
                <TableRow>
                  {question.answerOption.map((option, index) => {
                    return (
                      <TableCell
                        key={index}
                        align="center"
                        sx={{ border: 1, borderColor: 'divider', cursor: 'pointer' }}
                        // onClick={(event) => handleChoiceBarClick(event)}
                      >
                        {option.valueCoding.display}
                      </TableCell>
                    );
                  })}
                </TableRow>
              </TableBody>
            </Table>
          </Grid>
        </Grid>
      </Stack>
    </Box>
  );
};
