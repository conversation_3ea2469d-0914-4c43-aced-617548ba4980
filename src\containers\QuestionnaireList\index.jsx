'use client';
import React from 'react';
import { <PERSON>, Grid, <PERSON>ack, Button } from '@mui/material';
import { strings } from '../../utility/strings';
import { QuestionnaireTable } from './components/QuestionnaireTable';
import { useRef } from 'react';
import { pages } from '../CommonConstants';
import { HeaderStyle, PanelBorder } from '@/components';

/**
 * @function QuestionnaireList
 * @returns {JSX.Element}
 */

/*
list of available props:
  handleNavigation,
  questionnaireList,
  codebookDefaultTemplate,
  onImportCallback,
  handlePublishQuestionnaire
  handleEditQuestionnaire,
  handleDownloadCodebook,
  handleDuplicateQuestionnaire,
  handleDeleteQuestionnaire,
  handleExportQuestionnaire,
  HeaderStyle,
  PanelBorder,
*/
export const QuestionnaireList = (props) => {
  const { handleNavigation, questionnaireList, onImportCallback } = props;
  const importInputRef = useRef(null);

  const handleNewQuestionnaireNavigation = () => {
    handleNavigation(pages.questionnaireEditor);
  };

  const handleImport = (event) => {
    const widgetFile = event?.target?.files && event?.target?.files[0];

    const fileReader = new FileReader();
    fileReader.readAsText(widgetFile, 'UTF-8');
    fileReader.onload = async (readerEvent) => {
      let fhirQuestionnaire = JSON.parse(readerEvent.target.result);

      const response = await onImportCallback(fhirQuestionnaire);

      if (response?.success) {
        console.log('successfully imported');
      }
      importInputRef.current.value = null;
    };
  };

  return (
    <>
      <HeaderStyle>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item xs="auto">
            {strings.questionnaires}
          </Grid>
          <Grid item xs="auto">
            <Stack direction="row" spacing={2}>
              <Button component="label" variant="outlined">
                <input ref={importInputRef} type="file" accept="application/json" onChange={handleImport} hidden />
                {strings.import}
              </Button>
              <Button variant="contained" onClick={handleNewQuestionnaireNavigation}>
                {strings.new}
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </HeaderStyle>
      <PanelBorder>
        <Box sx={{ p: 2 }}>
          <QuestionnaireTable {...props} />
        </Box>
      </PanelBorder>
    </>
  );
};
