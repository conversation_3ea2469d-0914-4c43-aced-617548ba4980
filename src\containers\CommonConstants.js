import {
  bodyDiagramDefaultExtension,
  checkboxesDefaultExtension,
  dataGridDefaultExtension,
  datePickerDefaultExtension,
  displayQuestionDefaultExtension,
  dropdownDefaultExtension,
  integerDefaultExtension,
  largeButtonDefaultExtension,
  linearScaleDefaultExtension,
  numberDefaultExtension,
  numericSliderDefaultExtension,
  paragraphDefaultExtension,
  radioDefaultExtension,
  textDefaultExtension,
} from '@/utility/defaultExtensions';

export const pages = {
  questionnaireList: 'Questionnaire List',
  questionnaireEditor: 'Questionnaire Editor',
};

export const characterLength = {
  responseValidationLength: 10,
  labelLength: 250,
  questionnaireNameLength: 100,
  responseOptionLength: 255,
  itemTextLength: 500,
  descriptionAndExplanationLength: 1024,
  informationTextLength: 10000,
};

export const editorScreens = {
  PROPERTIES: 'PROPERTIES',
  ITEMS: 'ITEMS',
  SCORING: 'SCORING',
  REPORTS: 'REPORTS',
  HTML_TEMPLATE: 'HTML_TEMPLATE',
  PDF_TEMPLATE: 'PDF_TEMPLATE',
  CODE_BOOK: 'CODE_BOOK',
};
export const statuses = {
  draft: 'draft',
  active: 'active',
  published: 'published',
  final: 'final',
};
export const publishedStatuses = {
  no: 'no',
  public: 'public',
  private: 'private',
  both: 'both',
};

export const actions = {
  next: 'Next',
  previous: 'Previous',
  new: 'New',
  import: 'Import',
  templateGallery: 'Template Gallery',
};

export const modes = {
  editor: 'Editor',
  preview: 'Preview',
};

export const choiceQuestions = [
  {
    type: 'checkboxes',
    label: 'Checkboxes',
  },
  {
    type: 'dropdown',
    label: 'Dropdown Menu',
  },
  {
    type: 'largeButton',
    label: 'Large Buttons',
  },
  {
    type: 'radio',
    label: 'Radio Buttons',
  },
];

export const textQuestions = [
  {
    type: 'display',
    label: 'Information',
  },
  {
    type: 'integer',
    label: 'Integer',
  },
  {
    type: 'decimal',
    label: 'Number',
  },
  {
    type: 'paragraph',
    label: 'Paragraph',
  },
  {
    type: 'text',
    label: 'Text',
  },
];

export const dateTimeQuestion = [
  {
    type: 'datePicker',
    label: 'Date Picker',
  },
];

export const numericQuestion = [
  {
    type: 'linearScale',
    label: 'Linear Scale',
  },
  {
    type: 'numericSlider',
    label: 'Numeric Slider',
  },
];

export const otherQuestions = [
  {
    type: 'bodyDiagram',
    label: 'Body Diagram',
  },
  {
    type: 'dataGrid',
    label: 'Data Grid',
  },
];

export const selectionRules = {
  numQEq: 'NUM_Q_EQ',
  varComapre: 'VAR_COMPARE',
};

export const selectionNames = {
  selectRule: 'Select Rule',
  requiredResponsesCountFromSubset: 'Required responses count from subset',
  arithmeticComparison: 'Arithmetic Comparison',
  requiredResponses: 'Required Responses',
};

export const questionTypeOptions = [textQuestions, choiceQuestions, dateTimeQuestion, numericQuestion, otherQuestions];

export const questionTypes = {
  display: 'display',
  integer: 'integer',
  decimal: 'decimal',
  paragraph: 'paragraph',
  text: 'text',
  checkboxes: 'checkboxes',
  dropdown: 'dropdown',
  largeButton: 'largeButton',
  radio: 'radio',
  datePicker: 'datePicker',
  linearScale: 'linearScale',
  numericSlider: 'numericSlider',
  bodyDiagram: 'bodyDiagram',
  dataGrid: 'dataGrid',
  choice: 'choice',
};

export const questionTemplates = {
  display: { type: 'display', extension: displayQuestionDefaultExtension },
  integer: { type: 'integer', extension: integerDefaultExtension },
  decimal: { type: 'decimal', extension: numberDefaultExtension },
  paragraph: { type: 'text', extension: paragraphDefaultExtension },
  text: { type: 'text', extension: textDefaultExtension },
  checkboxes: { type: 'choice', answerOption: [], extension: checkboxesDefaultExtension },
  dropdown: { type: 'choice', answerOption: [], extension: dropdownDefaultExtension },
  largeButton: { type: 'choice', answerOption: [], extension: largeButtonDefaultExtension },
  radio: { type: 'choice', answerOption: [], extension: radioDefaultExtension },
  datePicker: { type: 'date', extension: datePickerDefaultExtension },
  linearScale: {
    type: 'choice',
    answerOption: [],
    linearConfig: { 'start-value': '', 'end-value': '', 'start-label': '', 'end-label': '' },
    extension: linearScaleDefaultExtension,
  },
  numericSlider: {
    type: 'integer',
    sliderConfig: { 'min-value': 0, 'max-value': 100, 'min-label': '', 'max-label': '', steps: 10 },
    extension: numericSliderDefaultExtension,
  },
  bodyDiagram: { type: 'choice', answerOption: [], extension: bodyDiagramDefaultExtension },
  dataGrid: { type: 'group', item: [], extension: dataGridDefaultExtension },
  choice: { type: 'choice', answerOption: [], extension: radioDefaultExtension },
};

export const LARGE_BUTTON_COLORS = {
  btnColor0: '#e74c3c',
  btnColor1: '#9b59b6',
  btnColor2: '#3498db',
  btnColor3: '#2ecc71',
  btnColor4: '#f1c40f',
  btnColor5: '#F06292',
  btnColor6: '#f39c12',
  btnColor7: '#d35400',
  btnColor8: '#16a085',
  btnColor9: '#27ae60',
  btnColor10: '#2980b9',
  btnColor11: '#8e44ad',
  btnColor12: '#2c3e50',
  btnColor13: '#e67e22',
  btnColor14: '#ecf0f1',
  btnColor15: '#95a5a6',
  btnColor16: '#c0392b',
  btnColor17: '#34495e',
  btnColor18: '#bdc3c7',
  btnColor19: '#7f8c8d',
  btnColor20: '#e74c3c',
  btnColor21: '#9b59b6',
  btnColor22: '#3498db',
  btnColor23: '#2ecc71',
  btnColor24: '#f1c40f',
  btnColor25: '#f06292',
  btnColor26: '#27ae60',
  btnColor27: '#2980b9',
  btnColor28: '#8e44ad',
  btnColor29: '#2c3e50',
  selected: '#424242',
};

export const dateTimeFormats = {
  placeholders: {
    date: 'yyyy-mm-dd',
    dateTime: 'yyyy-mm-dd hh:mm aa',
  },
  templates: {
    dateTimeWithSeconds: 'YYYY-MM-DD hh:mm:ss aa',
    date: 'YYYY-MM-DD',
  },
  display: {
    monthDayYear: 'MMM dd,yyyy',
    yearMonthDate: 'yyyy-MM-dd',
  },
};

export const SORTED_ASCENDING = 'sorted ascending';
export const SORTED_DESCENDING = 'sorted descending';
export const DRAFT = 'DRAFT';
export const PUBLISHED = 'PUBLISHED';
export const CANNOT_LOCATE_STRING = 'cannot locate string';
export const HTML = 'HTML';
export const PDF = 'PDF';
