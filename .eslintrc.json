{"env": {"browser": true, "es2021": true}, "extends": ["plugin:react/recommended", "airbnb", "prettier"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["import", "react", "prettier"], "rules": {"react/react-in-jsx-scope": "off", "react/jsx-filename-extension": "off", "react/prop-types": "off", "no-unused-vars": "warn", "max-len": ["error", {"code": 120, "ignoreUrls": true, "ignoreRegExpLiterals": true, "ignoreTrailingComments": true}]}}