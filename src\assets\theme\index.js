'use client';
import { createTheme } from '@mui/material/styles';

const globalTheme = createTheme({
  palette: {
    common: {
      white: '#FFFFFF',
      black: '#000000',
      cambianBlue: '#4D76A9',
      darkBlue: '#41638E',
      coconut: '#FDFDFD',
      lightGray: '#F0F0F0',
      mediumGray1: '#D9DAD9',
      mediumGray2: '#CBCBCB',
      darkGray1: '#595959',
      darkGray2: '#333333',
      cambianBackground: '#f5f8fa',
      skyBlue: '#47AAFF',
    },
  },
  typography: {
    fontWeight: {
      bold: 700,
      semibold: 600,
      medium: 500,
      regular: 400,
      light: 300,
    },
  },
});

// Here is an example json theme:
//   https://gist.githubusercontent.com/phusick/b5a114b9fd4baeca339b12160139635d/raw/c93c0b71bab55bf2bde09c3a2052718faa445bdc/material-ui-theme.json

const CambianTheme = createTheme({
  typography: {
    fontFamily: 'inherit',
    button: {
      textTransform: 'none',
    },
    h1: {
      fontSize: 24,
      fontWeight: globalTheme.typography.fontWeight.regular,
      color: globalTheme.palette.common.cambianBlue,
    },
    h2: {
      fontSize: 22,
      fontWeight: globalTheme.typography.fontWeight.regular,
      color: globalTheme.palette.common.cambianBlue,
    },
    h3: {
      fontSize: 20,
      fontWeight: globalTheme.typography.fontWeight.regular,
      color: globalTheme.palette.common.cambianBlue,
    },
    h4: {
      fontSize: 18,
      fontWeight: globalTheme.typography.fontWeight.regular,
      color: globalTheme.palette.common.cambianBlue,
    },
    // body1: { // default variant
    //   fontSize: 16,
    //   fontWeight: globalTheme.typography.fontWeight.regular,
    //   color: globalTheme.palette.common.black,
    // },
    bodyTitle: {
      fontSize: 16,
      fontWeight: globalTheme.typography.fontWeight.medium,
      color: globalTheme.palette.common.darkGray2,
    },
  },
  palette: {
    cambianCommon: globalTheme.palette.common,
    primary: {
      main: globalTheme.palette.common.cambianBlue,
      hover: globalTheme.palette.common.darkBlue,
    },
    background: {
      default: globalTheme.palette.common.white,
      primary: globalTheme.palette.common.cambianBackground,
      secondary: globalTheme.palette.common.lightGray,
    },
    text: {
      primary: globalTheme.palette.common.darkGray2,
      secondary: globalTheme.palette.common.cambianBlue,
      subHeading: globalTheme.palette.common.skyBlue,
    },
  },
  components: {
    MuiTooltip: {
      defaultProps: {
        arrow: true,
        enterTouchDelay: 500,
        enterDelay: 500,
      },
      styleOverrides: {
        tooltip: ({ theme }) => ({
          backgroundColor: theme.palette.common.white,
          color: 'rgba(0, 0, 0, 0.87)',
          boxShadow: theme.shadows[1],
          marginTop: '0px',
        }),
        arrow: ({ theme }) => ({
          '&:before': {
            border: `1px solid ${theme.palette.common.white}`,
            backgroundColor: 'white',
            boxShadow: '2px 2px 5px rgba(0, 0, 0, 0.2)',
          },
        }),
        popper: {
          '&[data-popper-placement*="bottom-start"] .MuiTooltip-tooltip': {
            marginTop: '0px',
            marginLeft: '20px',
          },
          '&[data-popper-placement*="bottom"] .MuiTooltip-tooltip': {
            marginTop: '5px',
            marginLeft: '20px',
          },
          '&[data-popper-placement*="top"] .MuiTooltip-tooltip': {
            marginBottom: '5px',
          },
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: ({ theme }) => ({
          fontSize: '1rem',
        }),
      },
    },
    MuiButton: {
      defaultProps: {
        disableElevation: true,
        disableRipple: true, // No more ripple!
      },
      styleOverrides: {
        outlinedPrimary: {
          backgroundColor: globalTheme.palette.common.white,
        },
        linkPrimary: {
          color: globalTheme.palette.common.cambianBlue,
        },
        contained: {
          fontSize: '16px',
        },
        outlined: {
          fontSize: '16px',
        },
      },
    },
    MuiIconButton: {
      defaultProps: {
        disableRipple: true, // No more ripple!
      },
      styleOverrides: {
        outlinedPrimary: {
          backgroundColor: globalTheme.palette.common.white,
        },
        linkPrimary: {
          color: globalTheme.palette.common.skyBlue,
        },
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: ({ theme }) => ({
          //border: '1px solid',
          //borderColor: theme.palette.divider,
        }),
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderBottom: '1px solid',
          borderColor: theme.palette.divider,
        }),
      },
    },
    MuiSwitch: {
      styleOverrides: {
        switchBase: {
          color: globalTheme.palette.common.mediumGray2,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        colorDefault: {
          backgroundColor: globalTheme.palette.common.white,
        },
      },
    },
    MuiPaper: {
      defaultProps: {
        variant: 'outlined',
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '& > *': {
            borderRight: '1px solid #0000001f',
          },
          '.clientInfoTables & > *': {
            borderRight: '0px',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        head: {
          fontWeight: globalTheme.typography.fontWeight.semibold,
          '.clientInfoTables &': {
            borderTop: '1px solid #0000001f',
          },
        },
        body: {
          fontWeight: globalTheme.typography.fontWeight.regular,
          '.clientInfoTables &': {
            borderBottom: '0px',
          },
        },
      },
    },
    MuiDataGrid: {
      styleOverrides: {
        columnHeaderTitle: {
          fontWeight: globalTheme.typography.fontWeight.semibold,
        },
        columnHeader: {
          borderRight: '1px solid #0000001f',
        },
        cell: {
          fontWeight: globalTheme.typography.fontWeight.regular,
          borderRight: '1px solid #0000001f',
        },
      },
    },
    MuiSelect: {
      defaultProps: {
        size: 'small',
      },
      styleOverrides: {
        root: {
          maxWidth: '500px',
          width: '100%',
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          maxWidth: '500px',
          width: '100%',
        },
      },
    },
    MuiInputLabel: {
      styleOverrides: {
        root: ({ theme }) => ({
          '&:not(.Mui-focused):not(.Mui-error)': {
            color: theme.palette.grey[500],
          },
        }),
      },
    },
    MuiTextField: {
      defaultProps: {
        size: 'small',
      },
      styleOverrides: {
        root: {
          '& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
            display: 'none',
          },
          maxWidth: '500px',
          width: '100%',
        },
      },
    },
  },
});

export { CambianTheme };
