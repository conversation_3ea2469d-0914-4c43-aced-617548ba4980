import React, { useState } from 'react';
import { Paper, Typography } from '@mui/material';
import { strings } from '../../../../utility/strings';
import { editorScreens } from '@/containers/CommonConstants';

const links = [
  {
    id: 0,
    label: strings.items,
    screen: editorScreens.ITEMS,
  },
  {
    id: 1,
    label: strings.scoring,
    screen: editorScreens.SCORING,
  },
  {
    id: 2,
    label: strings.reports,
    screen: editorScreens.REPORTS,
  },
];

export const Editors = (props) => {
  const { handleEditorScreenNavigation } = props;
  const [selectedScreen, setSelectedScreen] = useState(editorScreens.ITEMS);

  const handleLinkClick = (screen) => {
    setSelectedScreen(screen);
    handleEditorScreenNavigation(screen);
  };

  return (
    <Paper sx={{ p: 2, maxHeight: 180 }}>
      {links.map((link) => (
        <Typography
          key={link.id}
          sx={{
            cursor: 'pointer',
            my: 1,
            textDecoration: selectedScreen === link.screen ? 'underline' : 'none',
            padding: '8px',
          }}
          onClick={() => handleLinkClick(link.screen)}
        >
          {link.label}
        </Typography>
      ))}
    </Paper>
  );
};
