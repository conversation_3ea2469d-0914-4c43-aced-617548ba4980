import React, { useEffect, useState, useContext } from 'react';
import { <PERSON><PERSON><PERSON>, Button, Grid, TextField, Modal, Box, Checkbox, FormGroup, FormControlLabel } from '@mui/material';
import { strings } from '@/utility/strings';
import { selectionNames } from '@/containers/CommonConstants';
import { QuestionnaireContext } from '@/context';
import { SELECT_ITEM_REQUIRED_RESPONSES, ADD_LEAST_SELECTED_NUMBER } from '@/context/scoring';
import { ScoringContext } from '@/context';

export const ScoringModal = (props) => {
  const { handleClose, open, condition, items, keyVal } = props;
  const [itemFlatArray, setItemFlatArray] = useState([]);
  const { questionnaireState: questionnairePages } = useContext(QuestionnaireContext);
  const { scoringState: variables, dispatchScoringAction } = useContext(ScoringContext);

  const closeContainerStyle = {
    width: '100%',
    p: 2,
    borderTop: '1px solid #e5e5e5',
    display: 'flex',
    justifyContent: 'flex-end',
  };

  const numberItemsStyle = {
    position: 'relative',
    maxWidth: '100%',
  };

  const ModalStyle = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 600,
    bgcolor: 'background.paper',
    border: '1px solid rgba(0, 0, 0, 0.2)',
    borderRadius: '6px',
    boxShadow: 24,
  };

  useEffect(() => {
    const itemArrFlattened = questionnairePages.flatMap(
      (itemArr) => {
        return itemArr.item.map((item) => {
          return item.text;
        });
      },
      [variables],
    );

    setItemFlatArray(itemArrFlattened);
    flattenItems();
  }, [variables]);

  const handleChecked = (key) => {
    if (condition.selectionName === selectionNames.requiredResponsesCountFromSubset) {
      const match = condition.selectionRule.match(/\$[^$]*\$(.*)/);
      if (match[1][key] === '1') {
        return true;
      }
    } else {
      if (condition.selectionRule[key] === '1') {
        return true;
      }
    }
    return false;
  };

  const handleCheckedAll = () => {
    let stringLength = condition.selectionRule.length;
    if (condition.selectionName === selectionNames.requiredResponsesCountFromSubset) {
      for (let i = 0; i < stringLength; i++) {
        if (condition.selectionRule[i + 11] === 'X') {
          return false;
        }
      }
    } else {
      for (let i = 0; i < stringLength; i++) {
        if (condition.selectionRule[i] === 'X') {
          return false;
        }
      }
    }
    return true;
  };

  const flattenItems = (keyValOrder) => {
    return (
      <FormGroup
        sx={{ mt: 2.5, paddingLeft: 5 }}
        onChange={(event) =>
          dispatchScoringAction({
            type: SELECT_ITEM_REQUIRED_RESPONSES,
            value: event.target.name,
            key: keyValOrder,
            selectionName: condition.selectionName,
          })
        }
      >
        <FormControlLabel
          control={<Checkbox size="small" />}
          name={'-1'}
          label="Select All"
          checked={handleCheckedAll()}
        />
        {itemFlatArray.map((item, key) => (
          <FormControlLabel
            key={key}
            control={<Checkbox size="small" />}
            label={item}
            name={`${key}`}
            checked={handleChecked(key)}
          />
        ))}
      </FormGroup>
    );
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box sx={ModalStyle}>
        <Grid container sx={{ width: '100%' }} direction="column" justifyContent="center">
          <Grid item sx={{ width: '100%', p: 2, borderBottom: '1px solid #e5e5e5' }}>
            <Typography variant="h3">{strings.mandatoryItems}</Typography>
          </Grid>
          <Grid item sx={{ padding: 2 }}>
            <p>{strings.selectMandatoryItems}</p>
            {flattenItems(keyVal)}
            {condition.selectionName === selectionNames.requiredResponsesCountFromSubset && (
              <div style={{ marginTop: 20, width: '40' }}>
                {strings.scoringModalText1}
                <span>
                  <TextField
                    onChange={(event) =>
                      dispatchScoringAction({ type: ADD_LEAST_SELECTED_NUMBER, value: event.target.value, key: keyVal })
                    }
                    value={condition.subSetCount}
                    sx={numberItemsStyle}
                  ></TextField>
                </span>
                {strings.scoringModalText2}
              </div>
            )}
          </Grid>
          <Grid item sx={closeContainerStyle}>
            <Button onClick={handleClose} variant="contained">
              Close
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Modal>
  );
};
